# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
subdir = src/extensions/compact
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_python_devel.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)" "$(DESTDIR)$(libfstdir)"
LTLIBRARIES = $(lib_LTLIBRARIES) $(libfst_LTLIBRARIES)
compact16_acceptor_fst_la_LIBADD =
am_compact16_acceptor_fst_la_OBJECTS = compact16_acceptor-fst.lo
compact16_acceptor_fst_la_OBJECTS =  \
	$(am_compact16_acceptor_fst_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
compact16_acceptor_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) \
	$(compact16_acceptor_fst_la_LDFLAGS) $(LDFLAGS) -o $@
compact16_string_fst_la_LIBADD =
am_compact16_string_fst_la_OBJECTS = compact16_string-fst.lo
compact16_string_fst_la_OBJECTS =  \
	$(am_compact16_string_fst_la_OBJECTS)
compact16_string_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) $(compact16_string_fst_la_LDFLAGS) \
	$(LDFLAGS) -o $@
compact16_unweighted_fst_la_LIBADD =
am_compact16_unweighted_fst_la_OBJECTS = compact16_unweighted-fst.lo
compact16_unweighted_fst_la_OBJECTS =  \
	$(am_compact16_unweighted_fst_la_OBJECTS)
compact16_unweighted_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) \
	$(compact16_unweighted_fst_la_LDFLAGS) $(LDFLAGS) -o $@
compact16_unweighted_acceptor_fst_la_LIBADD =
am_compact16_unweighted_acceptor_fst_la_OBJECTS =  \
	compact16_unweighted_acceptor-fst.lo
compact16_unweighted_acceptor_fst_la_OBJECTS =  \
	$(am_compact16_unweighted_acceptor_fst_la_OBJECTS)
compact16_unweighted_acceptor_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) \
	--tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link \
	$(CXXLD) $(AM_CXXFLAGS) $(CXXFLAGS) \
	$(compact16_unweighted_acceptor_fst_la_LDFLAGS) $(LDFLAGS) -o \
	$@
compact16_weighted_string_fst_la_LIBADD =
am_compact16_weighted_string_fst_la_OBJECTS =  \
	compact16_weighted_string-fst.lo
compact16_weighted_string_fst_la_OBJECTS =  \
	$(am_compact16_weighted_string_fst_la_OBJECTS)
compact16_weighted_string_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) \
	--tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link \
	$(CXXLD) $(AM_CXXFLAGS) $(CXXFLAGS) \
	$(compact16_weighted_string_fst_la_LDFLAGS) $(LDFLAGS) -o $@
compact64_acceptor_fst_la_LIBADD =
am_compact64_acceptor_fst_la_OBJECTS = compact64_acceptor-fst.lo
compact64_acceptor_fst_la_OBJECTS =  \
	$(am_compact64_acceptor_fst_la_OBJECTS)
compact64_acceptor_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) \
	$(compact64_acceptor_fst_la_LDFLAGS) $(LDFLAGS) -o $@
compact64_string_fst_la_LIBADD =
am_compact64_string_fst_la_OBJECTS = compact64_string-fst.lo
compact64_string_fst_la_OBJECTS =  \
	$(am_compact64_string_fst_la_OBJECTS)
compact64_string_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) $(compact64_string_fst_la_LDFLAGS) \
	$(LDFLAGS) -o $@
compact64_unweighted_fst_la_LIBADD =
am_compact64_unweighted_fst_la_OBJECTS = compact64_unweighted-fst.lo
compact64_unweighted_fst_la_OBJECTS =  \
	$(am_compact64_unweighted_fst_la_OBJECTS)
compact64_unweighted_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) \
	$(compact64_unweighted_fst_la_LDFLAGS) $(LDFLAGS) -o $@
compact64_unweighted_acceptor_fst_la_LIBADD =
am_compact64_unweighted_acceptor_fst_la_OBJECTS =  \
	compact64_unweighted_acceptor-fst.lo
compact64_unweighted_acceptor_fst_la_OBJECTS =  \
	$(am_compact64_unweighted_acceptor_fst_la_OBJECTS)
compact64_unweighted_acceptor_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) \
	--tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link \
	$(CXXLD) $(AM_CXXFLAGS) $(CXXFLAGS) \
	$(compact64_unweighted_acceptor_fst_la_LDFLAGS) $(LDFLAGS) -o \
	$@
compact64_weighted_string_fst_la_LIBADD =
am_compact64_weighted_string_fst_la_OBJECTS =  \
	compact64_weighted_string-fst.lo
compact64_weighted_string_fst_la_OBJECTS =  \
	$(am_compact64_weighted_string_fst_la_OBJECTS)
compact64_weighted_string_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) \
	--tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link \
	$(CXXLD) $(AM_CXXFLAGS) $(CXXFLAGS) \
	$(compact64_weighted_string_fst_la_LDFLAGS) $(LDFLAGS) -o $@
compact8_acceptor_fst_la_LIBADD =
am_compact8_acceptor_fst_la_OBJECTS = compact8_acceptor-fst.lo
compact8_acceptor_fst_la_OBJECTS =  \
	$(am_compact8_acceptor_fst_la_OBJECTS)
compact8_acceptor_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) $(compact8_acceptor_fst_la_LDFLAGS) \
	$(LDFLAGS) -o $@
compact8_string_fst_la_LIBADD =
am_compact8_string_fst_la_OBJECTS = compact8_string-fst.lo
compact8_string_fst_la_OBJECTS = $(am_compact8_string_fst_la_OBJECTS)
compact8_string_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) $(compact8_string_fst_la_LDFLAGS) \
	$(LDFLAGS) -o $@
compact8_unweighted_fst_la_LIBADD =
am_compact8_unweighted_fst_la_OBJECTS = compact8_unweighted-fst.lo
compact8_unweighted_fst_la_OBJECTS =  \
	$(am_compact8_unweighted_fst_la_OBJECTS)
compact8_unweighted_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) \
	$(compact8_unweighted_fst_la_LDFLAGS) $(LDFLAGS) -o $@
compact8_unweighted_acceptor_fst_la_LIBADD =
am_compact8_unweighted_acceptor_fst_la_OBJECTS =  \
	compact8_unweighted_acceptor-fst.lo
compact8_unweighted_acceptor_fst_la_OBJECTS =  \
	$(am_compact8_unweighted_acceptor_fst_la_OBJECTS)
compact8_unweighted_acceptor_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) \
	--tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link \
	$(CXXLD) $(AM_CXXFLAGS) $(CXXFLAGS) \
	$(compact8_unweighted_acceptor_fst_la_LDFLAGS) $(LDFLAGS) -o \
	$@
compact8_weighted_string_fst_la_LIBADD =
am_compact8_weighted_string_fst_la_OBJECTS =  \
	compact8_weighted_string-fst.lo
compact8_weighted_string_fst_la_OBJECTS =  \
	$(am_compact8_weighted_string_fst_la_OBJECTS)
compact8_weighted_string_fst_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) \
	$(compact8_weighted_string_fst_la_LDFLAGS) $(LDFLAGS) -o $@
libfstcompact_la_LIBADD =
am_libfstcompact_la_OBJECTS = compact8_acceptor-fst.lo \
	compact8_string-fst.lo compact8_unweighted-fst.lo \
	compact8_unweighted_acceptor-fst.lo \
	compact8_weighted_string-fst.lo compact16_acceptor-fst.lo \
	compact16_string-fst.lo compact16_unweighted-fst.lo \
	compact16_unweighted_acceptor-fst.lo \
	compact16_weighted_string-fst.lo compact64_acceptor-fst.lo \
	compact64_string-fst.lo compact64_unweighted-fst.lo \
	compact64_unweighted_acceptor-fst.lo \
	compact64_weighted_string-fst.lo
libfstcompact_la_OBJECTS = $(am_libfstcompact_la_OBJECTS)
libfstcompact_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) $(libfstcompact_la_LDFLAGS) \
	$(LDFLAGS) -o $@
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = 
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/compact16_acceptor-fst.Plo \
	./$(DEPDIR)/compact16_string-fst.Plo \
	./$(DEPDIR)/compact16_unweighted-fst.Plo \
	./$(DEPDIR)/compact16_unweighted_acceptor-fst.Plo \
	./$(DEPDIR)/compact16_weighted_string-fst.Plo \
	./$(DEPDIR)/compact64_acceptor-fst.Plo \
	./$(DEPDIR)/compact64_string-fst.Plo \
	./$(DEPDIR)/compact64_unweighted-fst.Plo \
	./$(DEPDIR)/compact64_unweighted_acceptor-fst.Plo \
	./$(DEPDIR)/compact64_weighted_string-fst.Plo \
	./$(DEPDIR)/compact8_acceptor-fst.Plo \
	./$(DEPDIR)/compact8_string-fst.Plo \
	./$(DEPDIR)/compact8_unweighted-fst.Plo \
	./$(DEPDIR)/compact8_unweighted_acceptor-fst.Plo \
	./$(DEPDIR)/compact8_weighted_string-fst.Plo
am__mv = mv -f
CXXCOMPILE = $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS)
LTCXXCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CXXFLAGS) $(CXXFLAGS)
AM_V_CXX = $(am__v_CXX_@AM_V@)
am__v_CXX_ = $(am__v_CXX_@AM_DEFAULT_V@)
am__v_CXX_0 = @echo "  CXX     " $@;
am__v_CXX_1 = 
CXXLD = $(CXX)
CXXLINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(AM_CXXFLAGS) \
	$(CXXFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CXXLD = $(am__v_CXXLD_@AM_V@)
am__v_CXXLD_ = $(am__v_CXXLD_@AM_DEFAULT_V@)
am__v_CXXLD_0 = @echo "  CXXLD   " $@;
am__v_CXXLD_1 = 
SOURCES = $(compact16_acceptor_fst_la_SOURCES) \
	$(compact16_string_fst_la_SOURCES) \
	$(compact16_unweighted_fst_la_SOURCES) \
	$(compact16_unweighted_acceptor_fst_la_SOURCES) \
	$(compact16_weighted_string_fst_la_SOURCES) \
	$(compact64_acceptor_fst_la_SOURCES) \
	$(compact64_string_fst_la_SOURCES) \
	$(compact64_unweighted_fst_la_SOURCES) \
	$(compact64_unweighted_acceptor_fst_la_SOURCES) \
	$(compact64_weighted_string_fst_la_SOURCES) \
	$(compact8_acceptor_fst_la_SOURCES) \
	$(compact8_string_fst_la_SOURCES) \
	$(compact8_unweighted_fst_la_SOURCES) \
	$(compact8_unweighted_acceptor_fst_la_SOURCES) \
	$(compact8_weighted_string_fst_la_SOURCES) \
	$(libfstcompact_la_SOURCES)
DIST_SOURCES = $(compact16_acceptor_fst_la_SOURCES) \
	$(compact16_string_fst_la_SOURCES) \
	$(compact16_unweighted_fst_la_SOURCES) \
	$(compact16_unweighted_acceptor_fst_la_SOURCES) \
	$(compact16_weighted_string_fst_la_SOURCES) \
	$(compact64_acceptor_fst_la_SOURCES) \
	$(compact64_string_fst_la_SOURCES) \
	$(compact64_unweighted_fst_la_SOURCES) \
	$(compact64_unweighted_acceptor_fst_la_SOURCES) \
	$(compact64_weighted_string_fst_la_SOURCES) \
	$(compact8_acceptor_fst_la_SOURCES) \
	$(compact8_string_fst_la_SOURCES) \
	$(compact8_unweighted_fst_la_SOURCES) \
	$(compact8_unweighted_acceptor_fst_la_SOURCES) \
	$(compact8_weighted_string_fst_la_SOURCES) \
	$(libfstcompact_la_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DL_LIBS = @DL_LIBS@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
FILECMD = @FILECMD@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = ../../lib/libfst.la -lm $(DL_LIBS)
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PYTHON = @PYTHON@
PYTHON_CPPFLAGS = @PYTHON_CPPFLAGS@
PYTHON_EXEC_PREFIX = @PYTHON_EXEC_PREFIX@
PYTHON_EXTRA_LDFLAGS = @PYTHON_EXTRA_LDFLAGS@
PYTHON_EXTRA_LIBS = @PYTHON_EXTRA_LIBS@
PYTHON_LIBS = @PYTHON_LIBS@
PYTHON_PLATFORM = @PYTHON_PLATFORM@
PYTHON_PREFIX = @PYTHON_PREFIX@
PYTHON_SITE_PKG = @PYTHON_SITE_PKG@
PYTHON_VERSION = @PYTHON_VERSION@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
libfstdir = @libfstdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
pkgpyexecdir = @pkgpyexecdir@
pkgpythondir = @pkgpythondir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
pyexecdir = @pyexecdir@
pythondir = @pythondir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
AM_CPPFLAGS = -I$(srcdir)/../../include $(ICU_CPPFLAGS)
libfst_LTLIBRARIES = compact8_acceptor-fst.la compact8_string-fst.la compact8_unweighted-fst.la compact8_unweighted_acceptor-fst.la compact8_weighted_string-fst.la compact16_acceptor-fst.la compact16_string-fst.la compact16_unweighted-fst.la compact16_unweighted_acceptor-fst.la compact16_weighted_string-fst.la compact64_acceptor-fst.la compact64_string-fst.la compact64_unweighted-fst.la compact64_unweighted_acceptor-fst.la compact64_weighted_string-fst.la
lib_LTLIBRARIES = libfstcompact.la
libfstcompact_la_SOURCES = compact8_acceptor-fst.cc compact8_string-fst.cc compact8_unweighted-fst.cc compact8_unweighted_acceptor-fst.cc compact8_weighted_string-fst.cc compact16_acceptor-fst.cc compact16_string-fst.cc compact16_unweighted-fst.cc compact16_unweighted_acceptor-fst.cc compact16_weighted_string-fst.cc compact64_acceptor-fst.cc compact64_string-fst.cc compact64_unweighted-fst.cc compact64_unweighted_acceptor-fst.cc compact64_weighted_string-fst.cc
libfstcompact_la_LDFLAGS = -version-info 26:0:0
compact8_acceptor_fst_la_SOURCES = compact8_acceptor-fst.cc
compact8_acceptor_fst_la_LDFLAGS = -avoid-version -module
compact8_string_fst_la_SOURCES = compact8_string-fst.cc
compact8_string_fst_la_LDFLAGS = -avoid-version -module
compact8_unweighted_fst_la_SOURCES = compact8_unweighted-fst.cc
compact8_unweighted_fst_la_LDFLAGS = -avoid-version -module
compact8_unweighted_acceptor_fst_la_SOURCES = compact8_unweighted_acceptor-fst.cc
compact8_unweighted_acceptor_fst_la_LDFLAGS = -avoid-version -module
compact8_weighted_string_fst_la_SOURCES = compact8_weighted_string-fst.cc
compact8_weighted_string_fst_la_LDFLAGS = -avoid-version -module
compact16_acceptor_fst_la_SOURCES = compact16_acceptor-fst.cc
compact16_acceptor_fst_la_LDFLAGS = -avoid-version -module
compact16_string_fst_la_SOURCES = compact16_string-fst.cc
compact16_string_fst_la_LDFLAGS = -avoid-version -module
compact16_unweighted_fst_la_SOURCES = compact16_unweighted-fst.cc
compact16_unweighted_fst_la_LDFLAGS = -avoid-version -module
compact16_unweighted_acceptor_fst_la_SOURCES = compact16_unweighted_acceptor-fst.cc
compact16_unweighted_acceptor_fst_la_LDFLAGS = -avoid-version -module
compact16_weighted_string_fst_la_SOURCES = compact16_weighted_string-fst.cc
compact16_weighted_string_fst_la_LDFLAGS = -avoid-version -module
compact64_acceptor_fst_la_SOURCES = compact64_acceptor-fst.cc
compact64_acceptor_fst_la_LDFLAGS = -avoid-version -module
compact64_string_fst_la_SOURCES = compact64_string-fst.cc
compact64_string_fst_la_LDFLAGS = -avoid-version -module
compact64_unweighted_fst_la_SOURCES = compact64_unweighted-fst.cc
compact64_unweighted_fst_la_LDFLAGS = -avoid-version -module
compact64_unweighted_acceptor_fst_la_SOURCES = compact64_unweighted_acceptor-fst.cc
compact64_unweighted_acceptor_fst_la_LDFLAGS = -avoid-version -module
compact64_weighted_string_fst_la_SOURCES = compact64_weighted_string-fst.cc
compact64_weighted_string_fst_la_LDFLAGS = -avoid-version -module
all: all-am

.SUFFIXES:
.SUFFIXES: .cc .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign src/extensions/compact/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign src/extensions/compact/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

install-libfstLTLIBRARIES: $(libfst_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(libfst_LTLIBRARIES)'; test -n "$(libfstdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libfstdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libfstdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libfstdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libfstdir)"; \
	}

uninstall-libfstLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(libfst_LTLIBRARIES)'; test -n "$(libfstdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libfstdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libfstdir)/$$f"; \
	done

clean-libfstLTLIBRARIES:
	-test -z "$(libfst_LTLIBRARIES)" || rm -f $(libfst_LTLIBRARIES)
	@list='$(libfst_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

compact16_acceptor-fst.la: $(compact16_acceptor_fst_la_OBJECTS) $(compact16_acceptor_fst_la_DEPENDENCIES) $(EXTRA_compact16_acceptor_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact16_acceptor_fst_la_LINK) -rpath $(libfstdir) $(compact16_acceptor_fst_la_OBJECTS) $(compact16_acceptor_fst_la_LIBADD) $(LIBS)

compact16_string-fst.la: $(compact16_string_fst_la_OBJECTS) $(compact16_string_fst_la_DEPENDENCIES) $(EXTRA_compact16_string_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact16_string_fst_la_LINK) -rpath $(libfstdir) $(compact16_string_fst_la_OBJECTS) $(compact16_string_fst_la_LIBADD) $(LIBS)

compact16_unweighted-fst.la: $(compact16_unweighted_fst_la_OBJECTS) $(compact16_unweighted_fst_la_DEPENDENCIES) $(EXTRA_compact16_unweighted_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact16_unweighted_fst_la_LINK) -rpath $(libfstdir) $(compact16_unweighted_fst_la_OBJECTS) $(compact16_unweighted_fst_la_LIBADD) $(LIBS)

compact16_unweighted_acceptor-fst.la: $(compact16_unweighted_acceptor_fst_la_OBJECTS) $(compact16_unweighted_acceptor_fst_la_DEPENDENCIES) $(EXTRA_compact16_unweighted_acceptor_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact16_unweighted_acceptor_fst_la_LINK) -rpath $(libfstdir) $(compact16_unweighted_acceptor_fst_la_OBJECTS) $(compact16_unweighted_acceptor_fst_la_LIBADD) $(LIBS)

compact16_weighted_string-fst.la: $(compact16_weighted_string_fst_la_OBJECTS) $(compact16_weighted_string_fst_la_DEPENDENCIES) $(EXTRA_compact16_weighted_string_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact16_weighted_string_fst_la_LINK) -rpath $(libfstdir) $(compact16_weighted_string_fst_la_OBJECTS) $(compact16_weighted_string_fst_la_LIBADD) $(LIBS)

compact64_acceptor-fst.la: $(compact64_acceptor_fst_la_OBJECTS) $(compact64_acceptor_fst_la_DEPENDENCIES) $(EXTRA_compact64_acceptor_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact64_acceptor_fst_la_LINK) -rpath $(libfstdir) $(compact64_acceptor_fst_la_OBJECTS) $(compact64_acceptor_fst_la_LIBADD) $(LIBS)

compact64_string-fst.la: $(compact64_string_fst_la_OBJECTS) $(compact64_string_fst_la_DEPENDENCIES) $(EXTRA_compact64_string_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact64_string_fst_la_LINK) -rpath $(libfstdir) $(compact64_string_fst_la_OBJECTS) $(compact64_string_fst_la_LIBADD) $(LIBS)

compact64_unweighted-fst.la: $(compact64_unweighted_fst_la_OBJECTS) $(compact64_unweighted_fst_la_DEPENDENCIES) $(EXTRA_compact64_unweighted_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact64_unweighted_fst_la_LINK) -rpath $(libfstdir) $(compact64_unweighted_fst_la_OBJECTS) $(compact64_unweighted_fst_la_LIBADD) $(LIBS)

compact64_unweighted_acceptor-fst.la: $(compact64_unweighted_acceptor_fst_la_OBJECTS) $(compact64_unweighted_acceptor_fst_la_DEPENDENCIES) $(EXTRA_compact64_unweighted_acceptor_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact64_unweighted_acceptor_fst_la_LINK) -rpath $(libfstdir) $(compact64_unweighted_acceptor_fst_la_OBJECTS) $(compact64_unweighted_acceptor_fst_la_LIBADD) $(LIBS)

compact64_weighted_string-fst.la: $(compact64_weighted_string_fst_la_OBJECTS) $(compact64_weighted_string_fst_la_DEPENDENCIES) $(EXTRA_compact64_weighted_string_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact64_weighted_string_fst_la_LINK) -rpath $(libfstdir) $(compact64_weighted_string_fst_la_OBJECTS) $(compact64_weighted_string_fst_la_LIBADD) $(LIBS)

compact8_acceptor-fst.la: $(compact8_acceptor_fst_la_OBJECTS) $(compact8_acceptor_fst_la_DEPENDENCIES) $(EXTRA_compact8_acceptor_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact8_acceptor_fst_la_LINK) -rpath $(libfstdir) $(compact8_acceptor_fst_la_OBJECTS) $(compact8_acceptor_fst_la_LIBADD) $(LIBS)

compact8_string-fst.la: $(compact8_string_fst_la_OBJECTS) $(compact8_string_fst_la_DEPENDENCIES) $(EXTRA_compact8_string_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact8_string_fst_la_LINK) -rpath $(libfstdir) $(compact8_string_fst_la_OBJECTS) $(compact8_string_fst_la_LIBADD) $(LIBS)

compact8_unweighted-fst.la: $(compact8_unweighted_fst_la_OBJECTS) $(compact8_unweighted_fst_la_DEPENDENCIES) $(EXTRA_compact8_unweighted_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact8_unweighted_fst_la_LINK) -rpath $(libfstdir) $(compact8_unweighted_fst_la_OBJECTS) $(compact8_unweighted_fst_la_LIBADD) $(LIBS)

compact8_unweighted_acceptor-fst.la: $(compact8_unweighted_acceptor_fst_la_OBJECTS) $(compact8_unweighted_acceptor_fst_la_DEPENDENCIES) $(EXTRA_compact8_unweighted_acceptor_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact8_unweighted_acceptor_fst_la_LINK) -rpath $(libfstdir) $(compact8_unweighted_acceptor_fst_la_OBJECTS) $(compact8_unweighted_acceptor_fst_la_LIBADD) $(LIBS)

compact8_weighted_string-fst.la: $(compact8_weighted_string_fst_la_OBJECTS) $(compact8_weighted_string_fst_la_DEPENDENCIES) $(EXTRA_compact8_weighted_string_fst_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(compact8_weighted_string_fst_la_LINK) -rpath $(libfstdir) $(compact8_weighted_string_fst_la_OBJECTS) $(compact8_weighted_string_fst_la_LIBADD) $(LIBS)

libfstcompact.la: $(libfstcompact_la_OBJECTS) $(libfstcompact_la_DEPENDENCIES) $(EXTRA_libfstcompact_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(libfstcompact_la_LINK) -rpath $(libdir) $(libfstcompact_la_OBJECTS) $(libfstcompact_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact16_acceptor-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact16_string-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact16_unweighted-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact16_unweighted_acceptor-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact16_weighted_string-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact64_acceptor-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact64_string-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact64_unweighted-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact64_unweighted_acceptor-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact64_weighted_string-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact8_acceptor-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact8_string-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact8_unweighted-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact8_unweighted_acceptor-fst.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/compact8_weighted_string-fst.Plo@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.cc.o:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCXX_TRUE@	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ $<

.cc.obj:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCXX_TRUE@	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.cc.lo:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCXX_TRUE@	$(LTCXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LTCXXCOMPILE) -c -o $@ $<

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(LTLIBRARIES)
install-libfstLTLIBRARIES: install-libLTLIBRARIES

installdirs:
	for dir in "$(DESTDIR)$(libdir)" "$(DESTDIR)$(libfstdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libLTLIBRARIES clean-libfstLTLIBRARIES \
	clean-libtool mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/compact16_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact16_string-fst.Plo
	-rm -f ./$(DEPDIR)/compact16_unweighted-fst.Plo
	-rm -f ./$(DEPDIR)/compact16_unweighted_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact16_weighted_string-fst.Plo
	-rm -f ./$(DEPDIR)/compact64_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact64_string-fst.Plo
	-rm -f ./$(DEPDIR)/compact64_unweighted-fst.Plo
	-rm -f ./$(DEPDIR)/compact64_unweighted_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact64_weighted_string-fst.Plo
	-rm -f ./$(DEPDIR)/compact8_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact8_string-fst.Plo
	-rm -f ./$(DEPDIR)/compact8_unweighted-fst.Plo
	-rm -f ./$(DEPDIR)/compact8_unweighted_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact8_weighted_string-fst.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-libfstLTLIBRARIES

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-libLTLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/compact16_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact16_string-fst.Plo
	-rm -f ./$(DEPDIR)/compact16_unweighted-fst.Plo
	-rm -f ./$(DEPDIR)/compact16_unweighted_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact16_weighted_string-fst.Plo
	-rm -f ./$(DEPDIR)/compact64_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact64_string-fst.Plo
	-rm -f ./$(DEPDIR)/compact64_unweighted-fst.Plo
	-rm -f ./$(DEPDIR)/compact64_unweighted_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact64_weighted_string-fst.Plo
	-rm -f ./$(DEPDIR)/compact8_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact8_string-fst.Plo
	-rm -f ./$(DEPDIR)/compact8_unweighted-fst.Plo
	-rm -f ./$(DEPDIR)/compact8_unweighted_acceptor-fst.Plo
	-rm -f ./$(DEPDIR)/compact8_weighted_string-fst.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-libLTLIBRARIES uninstall-libfstLTLIBRARIES

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-generic clean-libLTLIBRARIES clean-libfstLTLIBRARIES \
	clean-libtool cscopelist-am ctags ctags-am distclean \
	distclean-compile distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am \
	install-libLTLIBRARIES install-libfstLTLIBRARIES install-man \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-compile mostlyclean-generic mostlyclean-libtool \
	pdf pdf-am ps ps-am tags tags-am uninstall uninstall-am \
	uninstall-libLTLIBRARIES uninstall-libfstLTLIBRARIES

.PRECIOUS: Makefile


# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
