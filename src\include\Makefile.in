# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
subdir = src/include
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_python_devel.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__nobase_include_HEADERS_DIST) \
	$(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
SOURCES =
DIST_SOURCES =
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__nobase_include_HEADERS_DIST = fst/accumulator.h fst/add-on.h \
	fst/arc-arena.h fst/arc-map.h fst/arc.h fst/arcfilter.h \
	fst/arcsort.h fst/bi-table.h fst/cache.h fst/cc-visitors.h \
	fst/closure.h fst/compact-fst.h fst/compat.h fst/complement.h \
	fst/compose-filter.h fst/compose.h fst/concat.h fst/connect.h \
	fst/const-fst.h fst/determinize.h fst/dfs-visit.h \
	fst/difference.h fst/disambiguate.h fst/edit-fst.h \
	fst/encode.h fst/epsnormalize.h fst/equal.h fst/equivalent.h \
	fst/error-weight.h fst/expanded-fst.h fst/expander-cache.h \
	fst/expectation-weight.h fst/factor-weight.h \
	fst/filter-state.h fst/flags.h fst/float-weight.h \
	fst/fst-decl.h fst/fst.h fst/fstlib.h fst/generic-register.h \
	fst/heap.h fst/icu.h fst/impl-to-fst.h fst/intersect.h \
	fst/interval-set.h fst/invert.h fst/isomorphic.h \
	fst/label-reachable.h fst/lexicographic-weight.h fst/lock.h \
	fst/log.h fst/lookahead-filter.h fst/lookahead-matcher.h \
	fst/mapped-file.h fst/matcher-fst.h fst/matcher.h fst/memory.h \
	fst/minimize.h fst/mutable-fst.h fst/pair-weight.h \
	fst/partition.h fst/power-weight.h fst/power-weight-mappers.h \
	fst/product-weight.h fst/project.h fst/properties.h \
	fst/prune.h fst/push.h fst/queue.h fst/randequivalent.h \
	fst/randgen.h fst/rational.h fst/register.h fst/relabel.h \
	fst/replace-util.h fst/replace.h fst/reverse.h fst/reweight.h \
	fst/rmepsilon.h fst/rmfinalepsilon.h fst/set-weight.h \
	fst/shortest-distance.h fst/shortest-path.h \
	fst/signed-log-weight.h fst/sparse-power-weight.h \
	fst/sparse-tuple-weight.h fst/state-map.h \
	fst/state-reachable.h fst/state-table.h fst/statesort.h \
	fst/string-weight.h fst/string.h fst/symbol-table-ops.h \
	fst/symbol-table.h fst/synchronize.h fst/test-properties.h \
	fst/topsort.h fst/tuple-weight.h fst/union-find.h \
	fst/union-weight.h fst/union.h fst/util.h fst/vector-fst.h \
	fst/verify.h fst/visit.h fst/windows_defs.inc fst/weight.h \
	fst/extensions/compress/compress.h \
	fst/extensions/compress/compressscript.h \
	fst/extensions/compress/elias.h \
	fst/extensions/far/compile-strings.h \
	fst/extensions/far/convert.h fst/extensions/far/create.h \
	fst/extensions/far/encode.h fst/extensions/far/equal.h \
	fst/extensions/far/extract.h fst/extensions/far/far.h \
	fst/extensions/far/far-class.h fst/extensions/far/farlib.h \
	fst/extensions/far/farscript.h fst/extensions/far/getters.h \
	fst/extensions/far/info.h fst/extensions/far/isomorphic.h \
	fst/extensions/far/map-reduce.h \
	fst/extensions/far/print-strings.h \
	fst/extensions/far/script-impl.h fst/extensions/far/stlist.h \
	fst/extensions/far/sttable.h \
	fst/extensions/linear/linear-fst-data-builder.h \
	fst/extensions/linear/linear-fst-data.h \
	fst/extensions/linear/linear-fst.h \
	fst/extensions/linear/linearscript.h \
	fst/extensions/linear/loglinear-apply.h \
	fst/extensions/linear/trie.h fst/extensions/mpdt/compose.h \
	fst/extensions/mpdt/expand.h fst/extensions/mpdt/info.h \
	fst/extensions/mpdt/mpdt.h fst/extensions/mpdt/mpdtlib.h \
	fst/extensions/mpdt/mpdtscript.h \
	fst/extensions/mpdt/read_write_utils.h \
	fst/extensions/mpdt/reverse.h \
	fst/extensions/ngram/bitmap-index.h \
	fst/extensions/ngram/ngram-fst.h fst/extensions/ngram/nthbit.h \
	fst/extensions/pdt/collection.h fst/extensions/pdt/compose.h \
	fst/extensions/pdt/expand.h fst/extensions/pdt/getters.h \
	fst/extensions/pdt/info.h fst/extensions/pdt/paren.h \
	fst/extensions/pdt/pdt.h fst/extensions/pdt/pdtlib.h \
	fst/extensions/pdt/pdtscript.h fst/extensions/pdt/replace.h \
	fst/extensions/pdt/reverse.h \
	fst/extensions/pdt/shortest-path.h fst/script/arc-class.h \
	fst/script/arcfilter-impl.h fst/script/arciterator-class.h \
	fst/script/arcsort.h fst/script/arg-packs.h \
	fst/script/closure.h fst/script/compile-impl.h \
	fst/script/compile.h fst/script/compose.h fst/script/concat.h \
	fst/script/connect.h fst/script/convert.h fst/script/decode.h \
	fst/script/determinize.h fst/script/difference.h \
	fst/script/disambiguate.h fst/script/draw-impl.h \
	fst/script/draw.h fst/script/encode.h \
	fst/script/encodemapper-class.h fst/script/epsnormalize.h \
	fst/script/equal.h fst/script/equivalent.h \
	fst/script/fst-class.h fst/script/fstscript.h \
	fst/script/getters.h fst/script/info-impl.h fst/script/info.h \
	fst/script/intersect.h fst/script/invert.h \
	fst/script/isomorphic.h fst/script/map.h fst/script/minimize.h \
	fst/script/print-impl.h fst/script/print.h \
	fst/script/project.h fst/script/prune.h fst/script/push.h \
	fst/script/randequivalent.h fst/script/randgen.h \
	fst/script/relabel.h fst/script/replace.h fst/script/reverse.h \
	fst/script/reweight.h fst/script/rmepsilon.h \
	fst/script/script-impl.h fst/script/shortest-distance.h \
	fst/script/shortest-path.h fst/script/stateiterator-class.h \
	fst/script/synchronize.h fst/script/text-io.h \
	fst/script/topsort.h fst/script/union.h \
	fst/script/weight-class.h fst/script/verify.h \
	fst/extensions/special/phi-fst.h \
	fst/extensions/special/rho-fst.h \
	fst/extensions/special/sigma-fst.h fst/test/algo_test.h \
	fst/test/compactors.h fst/test/fst_test.h fst/test/rand-fst.h \
	fst/test/weight-tester.h
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(includedir)"
HEADERS = $(nobase_include_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DL_LIBS = @DL_LIBS@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
FILECMD = @FILECMD@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PYTHON = @PYTHON@
PYTHON_CPPFLAGS = @PYTHON_CPPFLAGS@
PYTHON_EXEC_PREFIX = @PYTHON_EXEC_PREFIX@
PYTHON_EXTRA_LDFLAGS = @PYTHON_EXTRA_LDFLAGS@
PYTHON_EXTRA_LIBS = @PYTHON_EXTRA_LIBS@
PYTHON_LIBS = @PYTHON_LIBS@
PYTHON_PLATFORM = @PYTHON_PLATFORM@
PYTHON_PREFIX = @PYTHON_PREFIX@
PYTHON_SITE_PKG = @PYTHON_SITE_PKG@
PYTHON_VERSION = @PYTHON_VERSION@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
libfstdir = @libfstdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
pkgpyexecdir = @pkgpyexecdir@
pkgpythondir = @pkgpythondir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
pyexecdir = @pyexecdir@
pythondir = @pythondir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
@HAVE_COMPRESS_TRUE@compress_include_headers = fst/extensions/compress/compress.h \
@HAVE_COMPRESS_TRUE@fst/extensions/compress/compressscript.h fst/extensions/compress/elias.h

@HAVE_FAR_TRUE@far_include_headers = fst/extensions/far/compile-strings.h \
@HAVE_FAR_TRUE@fst/extensions/far/convert.h fst/extensions/far/create.h \
@HAVE_FAR_TRUE@fst/extensions/far/encode.h fst/extensions/far/equal.h \
@HAVE_FAR_TRUE@fst/extensions/far/extract.h fst/extensions/far/far.h \
@HAVE_FAR_TRUE@fst/extensions/far/far-class.h fst/extensions/far/farlib.h \
@HAVE_FAR_TRUE@fst/extensions/far/farscript.h fst/extensions/far/getters.h \
@HAVE_FAR_TRUE@fst/extensions/far/info.h fst/extensions/far/isomorphic.h \
@HAVE_FAR_TRUE@fst/extensions/far/map-reduce.h fst/extensions/far/print-strings.h \
@HAVE_FAR_TRUE@fst/extensions/far/script-impl.h fst/extensions/far/stlist.h \
@HAVE_FAR_TRUE@fst/extensions/far/sttable.h

@HAVE_GRM_TRUE@far_include_headers = fst/extensions/far/compile-strings.h \
@HAVE_GRM_TRUE@fst/extensions/far/convert.h fst/extensions/far/create.h \
@HAVE_GRM_TRUE@fst/extensions/far/encode.h fst/extensions/far/equal.h \
@HAVE_GRM_TRUE@fst/extensions/far/extract.h fst/extensions/far/far.h \
@HAVE_GRM_TRUE@fst/extensions/far/far-class.h fst/extensions/far/farlib.h \
@HAVE_GRM_TRUE@fst/extensions/far/farscript.h fst/extensions/far/getters.h \
@HAVE_GRM_TRUE@fst/extensions/far/info.h fst/extensions/far/isomorphic.h \
@HAVE_GRM_TRUE@fst/extensions/far/map-reduce.h fst/extensions/far/print-strings.h \
@HAVE_GRM_TRUE@fst/extensions/far/script-impl.h fst/extensions/far/stlist.h \
@HAVE_GRM_TRUE@fst/extensions/far/sttable.h

@HAVE_FSTS_TRUE@linear_include_headers = fst/extensions/linear/linear-fst-data-builder.h \
@HAVE_FSTS_TRUE@fst/extensions/linear/linear-fst-data.h fst/extensions/linear/linear-fst.h \
@HAVE_FSTS_TRUE@fst/extensions/linear/linearscript.h fst/extensions/linear/loglinear-apply.h \
@HAVE_FSTS_TRUE@fst/extensions/linear/trie.h

@HAVE_LINEAR_TRUE@linear_include_headers = fst/extensions/linear/linear-fst-data-builder.h \
@HAVE_LINEAR_TRUE@fst/extensions/linear/linear-fst-data.h fst/extensions/linear/linear-fst.h \
@HAVE_LINEAR_TRUE@fst/extensions/linear/linearscript.h fst/extensions/linear/loglinear-apply.h \
@HAVE_LINEAR_TRUE@fst/extensions/linear/trie.h

@HAVE_FSTS_TRUE@ngram_include_headers = fst/extensions/ngram/bitmap-index.h \
@HAVE_FSTS_TRUE@fst/extensions/ngram/ngram-fst.h fst/extensions/ngram/nthbit.h

@HAVE_NGRAM_TRUE@ngram_include_headers = fst/extensions/ngram/bitmap-index.h \
@HAVE_NGRAM_TRUE@fst/extensions/ngram/ngram-fst.h fst/extensions/ngram/nthbit.h

@HAVE_GRM_TRUE@mpdt_include_headers = fst/extensions/mpdt/compose.h \
@HAVE_GRM_TRUE@fst/extensions/mpdt/expand.h fst/extensions/mpdt/info.h \
@HAVE_GRM_TRUE@fst/extensions/mpdt/mpdt.h fst/extensions/mpdt/mpdtlib.h \
@HAVE_GRM_TRUE@fst/extensions/mpdt/mpdtscript.h fst/extensions/mpdt/read_write_utils.h \
@HAVE_GRM_TRUE@fst/extensions/mpdt/reverse.h

@HAVE_MPDT_TRUE@mpdt_include_headers = fst/extensions/mpdt/compose.h \
@HAVE_MPDT_TRUE@fst/extensions/mpdt/expand.h fst/extensions/mpdt/info.h \
@HAVE_MPDT_TRUE@fst/extensions/mpdt/mpdt.h fst/extensions/mpdt/mpdtlib.h \
@HAVE_MPDT_TRUE@fst/extensions/mpdt/mpdtscript.h fst/extensions/mpdt/read_write_utils.h \
@HAVE_MPDT_TRUE@fst/extensions/mpdt/reverse.h

@HAVE_GRM_TRUE@pdt_include_headers = fst/extensions/pdt/collection.h \
@HAVE_GRM_TRUE@fst/extensions/pdt/compose.h fst/extensions/pdt/expand.h \
@HAVE_GRM_TRUE@fst/extensions/pdt/getters.h fst/extensions/pdt/info.h \
@HAVE_GRM_TRUE@fst/extensions/pdt/paren.h fst/extensions/pdt/pdt.h \
@HAVE_GRM_TRUE@fst/extensions/pdt/pdtlib.h fst/extensions/pdt/pdtscript.h \
@HAVE_GRM_TRUE@fst/extensions/pdt/replace.h fst/extensions/pdt/reverse.h \
@HAVE_GRM_TRUE@fst/extensions/pdt/shortest-path.h

@HAVE_PDT_TRUE@pdt_include_headers = fst/extensions/pdt/collection.h \
@HAVE_PDT_TRUE@fst/extensions/pdt/compose.h fst/extensions/pdt/expand.h \
@HAVE_PDT_TRUE@fst/extensions/pdt/getters.h fst/extensions/pdt/info.h \
@HAVE_PDT_TRUE@fst/extensions/pdt/paren.h fst/extensions/pdt/pdt.h \
@HAVE_PDT_TRUE@fst/extensions/pdt/pdtlib.h fst/extensions/pdt/pdtscript.h \
@HAVE_PDT_TRUE@fst/extensions/pdt/replace.h fst/extensions/pdt/reverse.h \
@HAVE_PDT_TRUE@fst/extensions/pdt/shortest-path.h

@HAVE_SPECIAL_TRUE@special_include_headers = fst/extensions/special/phi-fst.h \
@HAVE_SPECIAL_TRUE@fst/extensions/special/rho-fst.h fst/extensions/special/sigma-fst.h

script_include_headers = fst/script/arc-class.h \
fst/script/arcfilter-impl.h fst/script/arciterator-class.h \
fst/script/arcsort.h fst/script/arg-packs.h fst/script/closure.h \
fst/script/compile-impl.h fst/script/compile.h fst/script/compose.h \
fst/script/concat.h fst/script/connect.h fst/script/convert.h \
fst/script/decode.h fst/script/determinize.h fst/script/difference.h \
fst/script/disambiguate.h fst/script/draw-impl.h fst/script/draw.h \
fst/script/encode.h fst/script/encodemapper-class.h fst/script/epsnormalize.h \
fst/script/equal.h fst/script/equivalent.h fst/script/fst-class.h \
fst/script/fstscript.h fst/script/getters.h fst/script/info-impl.h \
fst/script/info.h fst/script/intersect.h fst/script/invert.h \
fst/script/isomorphic.h fst/script/map.h fst/script/minimize.h \
fst/script/print-impl.h fst/script/print.h fst/script/project.h \
fst/script/prune.h fst/script/push.h fst/script/randequivalent.h \
fst/script/randgen.h fst/script/relabel.h fst/script/replace.h \
fst/script/reverse.h fst/script/reweight.h fst/script/rmepsilon.h \
fst/script/script-impl.h fst/script/shortest-distance.h \
fst/script/shortest-path.h fst/script/stateiterator-class.h \
fst/script/synchronize.h fst/script/text-io.h fst/script/topsort.h \
fst/script/union.h fst/script/weight-class.h fst/script/verify.h

test_include_headers = fst/test/algo_test.h fst/test/compactors.h \
fst/test/fst_test.h fst/test/rand-fst.h fst/test/weight-tester.h

nobase_include_HEADERS = fst/accumulator.h fst/add-on.h fst/arc-arena.h \
fst/arc-map.h fst/arc.h fst/arcfilter.h fst/arcsort.h fst/bi-table.h \
fst/cache.h fst/cc-visitors.h fst/closure.h fst/compact-fst.h fst/compat.h \
fst/complement.h fst/compose-filter.h fst/compose.h fst/concat.h \
fst/connect.h fst/const-fst.h fst/determinize.h fst/dfs-visit.h \
fst/difference.h fst/disambiguate.h fst/edit-fst.h fst/encode.h \
fst/epsnormalize.h fst/equal.h fst/equivalent.h fst/error-weight.h \
fst/expanded-fst.h fst/expander-cache.h fst/expectation-weight.h \
fst/factor-weight.h fst/filter-state.h fst/flags.h fst/float-weight.h \
fst/fst-decl.h fst/fst.h fst/fstlib.h fst/generic-register.h fst/heap.h \
fst/icu.h fst/impl-to-fst.h fst/intersect.h fst/interval-set.h fst/invert.h \
fst/isomorphic.h fst/label-reachable.h fst/lexicographic-weight.h fst/lock.h \
fst/log.h fst/lookahead-filter.h fst/lookahead-matcher.h fst/mapped-file.h \
fst/matcher-fst.h fst/matcher.h fst/memory.h fst/minimize.h fst/mutable-fst.h \
fst/pair-weight.h fst/partition.h fst/power-weight.h \
fst/power-weight-mappers.h fst/product-weight.h fst/project.h \
fst/properties.h fst/prune.h fst/push.h fst/queue.h fst/randequivalent.h \
fst/randgen.h fst/rational.h fst/register.h fst/relabel.h fst/replace-util.h \
fst/replace.h fst/reverse.h fst/reweight.h fst/rmepsilon.h \
fst/rmfinalepsilon.h fst/set-weight.h fst/shortest-distance.h \
fst/shortest-path.h fst/signed-log-weight.h fst/sparse-power-weight.h \
fst/sparse-tuple-weight.h fst/state-map.h fst/state-reachable.h \
fst/state-table.h fst/statesort.h fst/string-weight.h fst/string.h \
fst/symbol-table-ops.h fst/symbol-table.h fst/synchronize.h \
fst/test-properties.h fst/topsort.h fst/tuple-weight.h fst/union-find.h \
fst/union-weight.h fst/union.h fst/util.h fst/vector-fst.h fst/verify.h \
fst/visit.h fst/windows_defs.inc fst/weight.h \
$(compress_include_headers) \
$(far_include_headers) \
$(linear_include_headers) \
$(mpdt_include_headers) \
$(ngram_include_headers) \
$(pdt_include_headers) \
$(script_include_headers) \
$(special_include_headers) \
$(test_include_headers)

all: all-am

.SUFFIXES:
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign src/include/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign src/include/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-nobase_includeHEADERS: $(nobase_include_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(nobase_include_HEADERS)'; test -n "$(includedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(includedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(includedir)" || exit 1; \
	fi; \
	$(am__nobase_list) | while read dir files; do \
	  xfiles=; for file in $$files; do \
	    if test -f "$$file"; then xfiles="$$xfiles $$file"; \
	    else xfiles="$$xfiles $(srcdir)/$$file"; fi; done; \
	  test -z "$$xfiles" || { \
	    test "x$$dir" = x. || { \
	      echo " $(MKDIR_P) '$(DESTDIR)$(includedir)/$$dir'"; \
	      $(MKDIR_P) "$(DESTDIR)$(includedir)/$$dir"; }; \
	    echo " $(INSTALL_HEADER) $$xfiles '$(DESTDIR)$(includedir)/$$dir'"; \
	    $(INSTALL_HEADER) $$xfiles "$(DESTDIR)$(includedir)/$$dir" || exit $$?; }; \
	done

uninstall-nobase_includeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(nobase_include_HEADERS)'; test -n "$(includedir)" || list=; \
	$(am__nobase_strip_setup); files=`$(am__nobase_strip)`; \
	dir='$(DESTDIR)$(includedir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(HEADERS)
installdirs:
	for dir in "$(DESTDIR)$(includedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-generic distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-nobase_includeHEADERS

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-generic mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-nobase_includeHEADERS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am check check-am clean clean-generic \
	clean-libtool cscopelist-am ctags ctags-am distclean \
	distclean-generic distclean-libtool distclean-tags distdir dvi \
	dvi-am html html-am info info-am install install-am \
	install-data install-data-am install-dvi install-dvi-am \
	install-exec install-exec-am install-html install-html-am \
	install-info install-info-am install-man \
	install-nobase_includeHEADERS install-pdf install-pdf-am \
	install-ps install-ps-am install-strip installcheck \
	installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-nobase_includeHEADERS

.PRECIOUS: Makefile


# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
