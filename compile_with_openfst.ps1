# Template script for compiling C++ programs with OpenFST
# Usage: .\compile_with_openfst.ps1 <source_file.cpp> [output_name]

param(
    [Parameter(Mandatory=$true)]
    [string]$SourceFile,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputName
)

Write-Host "Compiling with OpenFST..." -ForegroundColor Green

# Check if source file exists
if (!(Test-Path $SourceFile)) {
    Write-Host "Error: Source file not found: $SourceFile" -ForegroundColor Red
    exit 1
}

# Get output name
if (!$OutputName) {
    $OutputName = [System.IO.Path]::GetFileNameWithoutExtension($SourceFile) + ".exe"
}

# OpenFST paths (use environment variables if available, otherwise fallback to defaults)
$OPENFST_ROOT = if ($env:OPENFST_ROOT) { $env:OPENFST_ROOT } else { "f:\openfst-1.8.4" }
$OPENFST_INCLUDE = if ($env:OPENFST_INCLUDE) { $env:OPENFST_INCLUDE } else { "$OPENFST_ROOT\src\include" }
$OPENFST_LIB = if ($env:OPENFST_LIB) { $env:OPENFST_LIB } else { "$OPENFST_ROOT\build" }

# MSVC paths
$VS_ROOT = "D:\Microsoft Visual Studio\2022\Community"
$MSVC_PATH = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\bin\HostX86\x64"
$MSVC_INCLUDE = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\include"
$MSVC_ATLMFC_INCLUDE = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\ATLMFC\include"
$MSVC_AUX_INCLUDE = "$VS_ROOT\VC\Auxiliary\VS\include"
$WIN_SDK_INCLUDE = "D:\Windows Kits\10\include\10.0.26100.0"
$WIN_SDK_LIB = "D:\Windows Kits\10\lib\10.0.26100.0"
$MSVC_LIB = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\lib\x64"
$MSVC_ATLMFC_LIB = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64"

# Set environment variables for MSVC
$env:PATH = "$MSVC_PATH;$env:PATH"
$env:INCLUDE = "$MSVC_INCLUDE;$MSVC_ATLMFC_INCLUDE;$MSVC_AUX_INCLUDE;$WIN_SDK_INCLUDE\ucrt;$WIN_SDK_INCLUDE\um;$WIN_SDK_INCLUDE\shared;$WIN_SDK_INCLUDE\winrt;$WIN_SDK_INCLUDE\cppwinrt"
$env:LIB = "$MSVC_LIB;$MSVC_ATLMFC_LIB;$WIN_SDK_LIB\ucrt\x64;$WIN_SDK_LIB\um\x64"

# Compiler flags for OpenFST
$CXXFLAGS = @(
    "/std:c++17",                    # C++17 standard (required for OpenFST)
    "/EHsc",                         # Exception handling
    "/MD",                           # Multi-threaded DLL runtime
    "/O2",                           # Optimize for speed
    "/DWIN32",                       # Windows platform
    "/D_WIN32",                      # Windows platform
    "/DFST_NO_DYNAMIC_LINKING",      # Disable dynamic linking (required for static lib)
    "/DNOMINMAX",                    # Prevent min/max macro conflicts
    "/I`"$OPENFST_INCLUDE`"",        # OpenFST include directory
    "/I`"$OPENFST_ROOT`""            # Root directory for config.h
)

Write-Host "Source file: $SourceFile" -ForegroundColor Cyan
Write-Host "Output file: $OutputName" -ForegroundColor Cyan
Write-Host "OpenFST include: $OPENFST_INCLUDE" -ForegroundColor Cyan
Write-Host "OpenFST library: $OPENFST_LIB\fst.lib" -ForegroundColor Cyan

# Compile
$cmd = "cl.exe"
$cmdArgs = $CXXFLAGS + @($SourceFile, "/Fe:$OutputName", "$OPENFST_LIB\fst.lib")

Write-Host "Compiling..." -ForegroundColor Yellow
& $cmd $cmdArgs

if ($LASTEXITCODE -eq 0) {
    Write-Host "Compilation successful! Output: $OutputName" -ForegroundColor Green
    
    # Clean up intermediate files
    $objFile = [System.IO.Path]::GetFileNameWithoutExtension($SourceFile) + ".obj"
    if (Test-Path $objFile) {
        Remove-Item $objFile
    }
    
} else {
    Write-Host "Compilation failed!" -ForegroundColor Red
    exit 1
}
