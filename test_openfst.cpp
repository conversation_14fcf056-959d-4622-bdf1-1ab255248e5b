#include <iostream>
#include <fst/fstlib.h>

using namespace fst;

int main() {
    // Create a simple FST
    StdVectorFst fst;
    
    // Add states
    int s0 = fst.AddState();
    int s1 = fst.AddState();
    int s2 = fst.AddState();
    
    // Set start state
    fst.SetStart(s0);
    
    // Set final state
    fst.SetFinal(s2, StdArc::Weight::One());
    
    // Add arcs
    fst.AddArc(s0, StdArc(1, 1, StdArc::Weight::One(), s1));  // input:1, output:1, weight:1, to state s1
    fst.AddArc(s1, StdArc(2, 2, StdArc::Weight::One(), s2));  // input:2, output:2, weight:1, to state s2
    
    // Print FST info
    std::cout << "OpenFST Test Program" << std::endl;
    std::cout << "Number of states: " << fst.NumStates() << std::endl;
    std::cout << "Start state: " << fst.Start() << std::endl;
    
    // Check if FST is valid
    if (fst.Properties(kAccessible, true) & kAccessible) {
        std::cout << "FST is accessible (all states reachable from start state)" << std::endl;
    }
    
    std::cout << "OpenFST library is working correctly!" << std::endl;
    
    return 0;
}
