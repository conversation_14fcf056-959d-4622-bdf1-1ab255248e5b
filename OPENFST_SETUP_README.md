# OpenFST Setup for Windows with MSVC

This document describes how to use the OpenFST library that has been compiled with Microsoft Visual C++ (MSVC) on Windows 11.

## ✅ Installation Complete

OpenFST 1.8.4 has been successfully compiled and installed with the following configuration:

- **Compiler**: Microsoft Visual C++ 2022 (MSVC 19.44.35207.1)
- **Platform**: Windows x64
- **C++ Standard**: C++17
- **Build Type**: Static library with optimizations

## 📁 File Locations

- **Library**: `f:\openfst-1.8.4\build\fst.lib`
- **Headers**: `f:\openfst-1.8.4\src\include\`
- **Config**: `f:\openfst-1.8.4\config.h`

## 🔧 Environment Variables

The following environment variables have been set up:

```
OPENFST_ROOT=f:\openfst-1.8.4
OPENFST_INCLUDE=f:\openfst-1.8.4\src\include
OPENFST_LIB=f:\openfst-1.8.4\build
PATH includes: f:\openfst-1.8.4\build
INCLUDE includes: f:\openfst-1.8.4\src\include
LIB includes: f:\openfst-1.8.4\build
```

**Note**: You need to restart your terminal/IDE for environment variable changes to take effect.

## 🚀 Using OpenFST in Your Projects

### Method 1: Using the Compilation Script

Use the provided `compile_with_openfst.ps1` script:

```powershell
.\compile_with_openfst.ps1 your_program.cpp your_output.exe
```

### Method 2: Manual Compilation

```powershell
cl.exe /std:c++17 /EHsc /MD /O2 /DWIN32 /D_WIN32 /DFST_NO_DYNAMIC_LINKING /DNOMINMAX /I"f:\openfst-1.8.4\src\include" /I"f:\openfst-1.8.4" your_program.cpp /Fe:your_output.exe f:\openfst-1.8.4\build\fst.lib
```

### Method 3: Using Environment Variables

After restarting your terminal, you can use:

```powershell
cl.exe /std:c++17 /EHsc /MD /O2 /DWIN32 /D_WIN32 /DFST_NO_DYNAMIC_LINKING /DNOMINMAX /I"%OPENFST_INCLUDE%" /I"%OPENFST_ROOT%" your_program.cpp /Fe:your_output.exe "%OPENFST_LIB%\fst.lib"
```

## 📝 Required Compiler Flags

When compiling with OpenFST, always include these flags:

- `/std:c++17` - C++17 standard (required)
- `/DFST_NO_DYNAMIC_LINKING` - Disable dynamic linking
- `/DNOMINMAX` - Prevent Windows min/max macro conflicts
- `/I"path\to\openfst\include"` - Include directory
- Link against `fst.lib`

## 🧪 Testing Your Setup

A test program `example_demo.exe` has been created and verified to work. You can run it to test your installation:

```powershell
cd f:\openfst-1.8.4
.\example_demo.exe
```

## 📚 Example Code

Here's a minimal example:

```cpp
#include <iostream>
#include <fst/fstlib.h>

using namespace fst;

int main() {
    StdVectorFst fst;
    int s0 = fst.AddState();
    int s1 = fst.AddState();
    
    fst.SetStart(s0);
    fst.SetFinal(s1, StdArc::Weight::One());
    fst.AddArc(s0, StdArc(1, 1, StdArc::Weight::One(), s1));
    
    std::cout << "FST has " << fst.NumStates() << " states" << std::endl;
    return 0;
}
```

## 🔄 Re-running Setup

If you need to set up the environment variables again:

```powershell
.\setup_openfst_env.ps1
```

Or use the batch file version:

```batch
setup_openfst_env.bat
```

## ⚠️ Important Notes

1. **Restart Required**: After running the setup script, restart your terminal/IDE
2. **Static Linking**: This build uses static linking - no DLLs required
3. **C++17**: Your projects must use C++17 or later
4. **MSVC Only**: This build is specifically for MSVC compiler

## 🎯 Next Steps

You can now use OpenFST in any C++ project by:
1. Including the OpenFST headers: `#include <fst/fstlib.h>`
2. Using the compilation flags shown above
3. Linking against the static library

The OpenFST library is ready for finite state transducer operations!
