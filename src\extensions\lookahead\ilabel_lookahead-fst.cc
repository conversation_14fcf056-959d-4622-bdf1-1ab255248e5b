// Copyright 2005-2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the 'License');
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an 'AS IS' BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// See www.openfst.org for extensive documentation on this weighted
// finite-state transducer library.

#include <fst/accumulator.h>
#include <fst/arc.h>
#include <fst/const-fst.h>
#include <fst/lookahead-matcher.h>
#include <fst/matcher-fst.h>
#include <fst/matcher.h>
#include <fst/register.h>

namespace fst {

static FstRegisterer<StdILabelLookAheadFst>
    ILabelLookAheadFst_StdArc_registerer;
static FstRegisterer<MatcherFst<
    ConstFst<LogArc>,
    LabelLookAheadMatcher<SortedMatcher<ConstFst<LogArc>>,
                          ilabel_lookahead_flags, FastLogAccumulator<LogArc>>,
    ilabel_lookahead_fst_type, LabelLookAheadRelabeler<LogArc>>>
    ILabelLookAheadFst_LogArc_registerer;
static FstRegisterer<MatcherFst<
    ConstFst<Log64Arc>,
    LabelLookAheadMatcher<SortedMatcher<ConstFst<Log64Arc>>,
                          ilabel_lookahead_flags, FastLogAccumulator<Log64Arc>>,
    ilabel_lookahead_fst_type, LabelLookAheadRelabeler<Log64Arc>>>
    ILabelLookAheadFst_Log64Arc_registerer;

}  // namespace fst
