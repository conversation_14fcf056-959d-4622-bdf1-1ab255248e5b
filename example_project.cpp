// Example OpenFST project - demonstrates basic FST operations
// This can be used as a template for your own OpenFST projects

#include <iostream>
#include <fst/fstlib.h>

using namespace fst;

void createSimpleFST() {
    std::cout << "\n=== Creating a Simple FST ===" << std::endl;
    
    // Create a vector FST (mutable)
    StdVectorFst fst;
    
    // Add states
    int s0 = fst.AddState();  // Initial state
    int s1 = fst.AddState();  // Intermediate state
    int s2 = fst.AddState();  // Final state
    
    // Set start state
    fst.SetStart(s0);
    
    // Set final state with weight
    fst.SetFinal(s2, StdArc::Weight::One());
    
    // Add arcs: AddArc(from_state, Arc(input_label, output_label, weight, to_state))
    fst.AddArc(s0, StdArc(1, 1, StdArc::Weight::One(), s1));      // 1:1/1.0
    fst.AddArc(s1, StdArc(2, 2, StdArc::Weight::One(), s2));      // 2:2/1.0
    fst.AddArc(s0, StdArc(3, 3, StdArc::Weight::One(), s2));      // 3:3/1.0 (direct path)
    
    std::cout << "FST created with " << fst.NumStates() << " states" << std::endl;
    std::cout << "Start state: " << fst.Start() << std::endl;
    
    // Check FST properties
    if (fst.Properties(kAccessible, true) & kAccessible) {
        std::cout << "✓ FST is accessible" << std::endl;
    }
    if (fst.Properties(kCoAccessible, true) & kCoAccessible) {
        std::cout << "✓ FST is co-accessible" << std::endl;
    }
}

void demonstrateComposition() {
    std::cout << "\n=== FST Composition Example ===" << std::endl;
    
    // Create first FST: maps 'a' to 'x', 'b' to 'y'
    StdVectorFst fst1;
    int s0 = fst1.AddState();
    int s1 = fst1.AddState();
    fst1.SetStart(s0);
    fst1.SetFinal(s1, StdArc::Weight::One());
    fst1.AddArc(s0, StdArc(1, 10, StdArc::Weight::One(), s1));  // 1->10 (a->x)
    fst1.AddArc(s0, StdArc(2, 20, StdArc::Weight::One(), s1));  // 2->20 (b->y)
    
    // Create second FST: maps 'x' to 'X', 'y' to 'Y'
    StdVectorFst fst2;
    s0 = fst2.AddState();
    s1 = fst2.AddState();
    fst2.SetStart(s0);
    fst2.SetFinal(s1, StdArc::Weight::One());
    fst2.AddArc(s0, StdArc(10, 100, StdArc::Weight::One(), s1)); // 10->100 (x->X)
    fst2.AddArc(s0, StdArc(20, 200, StdArc::Weight::One(), s1)); // 20->200 (y->Y)
    
    // Compose the FSTs
    StdVectorFst composed;
    Compose(fst1, fst2, &composed);
    
    std::cout << "Composed FST has " << composed.NumStates() << " states" << std::endl;
    std::cout << "This FST maps: a->X and b->Y" << std::endl;
}

void demonstrateOptimization() {
    std::cout << "\n=== FST Optimization Example ===" << std::endl;
    
    // Create an FST with redundant states
    StdVectorFst fst;
    int s0 = fst.AddState();
    int s1 = fst.AddState();
    int s2 = fst.AddState();
    int s3 = fst.AddState();
    
    fst.SetStart(s0);
    fst.SetFinal(s2, StdArc::Weight::One());
    fst.SetFinal(s3, StdArc::Weight::One());
    
    // Add arcs that create equivalent states
    fst.AddArc(s0, StdArc(1, 1, StdArc::Weight::One(), s1));
    fst.AddArc(s1, StdArc(2, 2, StdArc::Weight::One(), s2));
    fst.AddArc(s1, StdArc(2, 2, StdArc::Weight::One(), s3));  // s2 and s3 are equivalent
    
    std::cout << "Original FST: " << fst.NumStates() << " states" << std::endl;
    
    // Minimize the FST
    Minimize(&fst);
    
    std::cout << "Minimized FST: " << fst.NumStates() << " states" << std::endl;
}

int main() {
    std::cout << "OpenFST Example Project" << std::endl;
    std::cout << "======================" << std::endl;
    
    try {
        createSimpleFST();
        demonstrateComposition();
        demonstrateOptimization();
        
        std::cout << "\n✓ All examples completed successfully!" << std::endl;
        std::cout << "\nYou can now use OpenFST in your own projects!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
