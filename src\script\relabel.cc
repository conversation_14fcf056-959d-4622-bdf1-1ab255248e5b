// Copyright 2005-2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the 'License');
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an 'AS IS' BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// See www.openfst.org for extensive documentation on this weighted
// finite-state transducer library.

#include <fst/script/relabel.h>

#include <cstdint>
#include <string>
#include <utility>
#include <vector>

#include <fst/symbol-table.h>
#include <fst/script/fst-class.h>
#include <fst/script/script-impl.h>

namespace fst {
namespace script {

void Relabel(MutableFstClass *ofst, const SymbolTable *old_isyms,
             const SymbolTable *relabel_isyms,
             const std::string &unknown_isymbol, bool attach_new_isyms,
             const SymbolTable *old_osyms, const SymbolTable *relabel_osyms,
             const std::string &unknown_osymbol, bool attach_new_osyms) {
  FstRelabelArgs1 args{ofst,
                       old_isyms,
                       relabel_isyms,
                       unknown_isymbol,
                       attach_new_isyms,
                       old_osyms,
                       relabel_osyms,
                       unknown_osymbol,
                       attach_new_osyms};
  Apply<Operation<FstRelabelArgs1>>("Relabel", ofst->ArcType(), &args);
}

void Relabel(MutableFstClass *ofst,
             const std::vector<std::pair<int64_t, int64_t>> &ipairs,
             const std::vector<std::pair<int64_t, int64_t>> &opairs) {
  FstRelabelArgs2 args{ofst, ipairs, opairs};
  Apply<Operation<FstRelabelArgs2>>("Relabel", ofst->ArcType(), &args);
}

REGISTER_FST_OPERATION_3ARCS(Relabel, FstRelabelArgs1);
REGISTER_FST_OPERATION_3ARCS(Relabel, FstRelabelArgs2);

}  // namespace script
}  // namespace fst
