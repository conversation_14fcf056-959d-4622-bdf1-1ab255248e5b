// Copyright 2005-2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the 'License');
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an 'AS IS' BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// See www.openfst.org for extensive documentation on this weighted
// finite-state transducer library.
//
// A finite-state archive (FAR) is used to store an indexable collection of
// FSTs in a single file. Utilities are provided to create FARs from FSTs,
// to iterate over FARs, and to extract specific FSTs from FARs.

#ifndef FST_EXTENSIONS_FAR_FARLIB_H_
#define FST_EXTENSIONS_FAR_FARLIB_H_

#include <fst/extensions/far/compile-strings.h>
#include <fst/extensions/far/create.h>
#include <fst/extensions/far/extract.h>
#include <fst/extensions/far/far.h>
#include <fst/extensions/far/getters.h>
#include <fst/extensions/far/info.h>
#include <fst/extensions/far/print-strings.h>

#endif  // FST_EXTENSIONS_FAR_FARLIB_H_
