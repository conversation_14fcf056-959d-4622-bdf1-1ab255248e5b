// Copyright 2005-2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the 'License');
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an 'AS IS' BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// See www.openfst.org for extensive documentation on this weighted
// finite-state transducer library.

#include <fst/flags.h>
#include <fst/weight.h>

DEFINE_double(delta, fst::kD<PERSON>ta, "Comparison/quantization delta");
DEFINE_string(
    map_type, "identity",
    "Map operation: one of \"arc_sum\", \"arc_unique\", "
    "\"identity\", \"input_epsilon\", \"invert\", \"output_epsilon\", "
    "\"plus (--weight)\", \"power (--power)\", \"quantize (--delta)\", "
    "\"rmweight\", \"superfinal\", \"times (--weight)\", "
    "\"to_log\", \"to_log64\", \"to_std\"");
DEFINE_double(power, 1.0, "Power parameter");
DEFINE_string(weight, "", "Weight parameter");

int fstmap_main(int argc, char **argv);

int main(int argc, char **argv) { return fstmap_main(argc, argv); }
