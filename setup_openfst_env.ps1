# Setup OpenFST Environment Variables
# This script sets up system environment variables for OpenFST

Write-Host "Setting up OpenFST environment variables..." -ForegroundColor Green

# OpenFST paths
$OPENFST_ROOT = "f:\openfst-1.8.4"
$OPENFST_INCLUDE = "$OPENFST_ROOT\src\include"
$OPENFST_LIB = "$OPENFST_ROOT\build"
$OPENFST_BIN = "$OPENFST_ROOT\build"  # For any future executables

# Check if paths exist
if (!(Test-Path $OPENFST_ROOT)) {
    Write-Host "Error: OpenFST root directory not found: $OPENFST_ROOT" -ForegroundColor Red
    exit 1
}

if (!(Test-Path $OPENFST_INCLUDE)) {
    Write-Host "Error: OpenFST include directory not found: $OPENFST_INCLUDE" -ForegroundColor Red
    exit 1
}

if (!(Test-Path $OPENFST_LIB)) {
    Write-Host "Error: OpenFST lib directory not found: $OPENFST_LIB" -ForegroundColor Red
    exit 1
}

Write-Host "Setting up system environment variables..." -ForegroundColor Yellow

try {
    # Set OpenFST environment variables
    [Environment]::SetEnvironmentVariable("OPENFST_ROOT", $OPENFST_ROOT, "User")
    [Environment]::SetEnvironmentVariable("OPENFST_INCLUDE", $OPENFST_INCLUDE, "User")
    [Environment]::SetEnvironmentVariable("OPENFST_LIB", $OPENFST_LIB, "User")
    
    # Get current PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    
    # Add OpenFST bin to PATH if not already present
    if ($currentPath -notlike "*$OPENFST_BIN*") {
        $newPath = "$currentPath;$OPENFST_BIN"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Host "Added OpenFST bin directory to PATH" -ForegroundColor Green
    } else {
        Write-Host "OpenFST bin directory already in PATH" -ForegroundColor Yellow
    }
    
    # Get current INCLUDE environment variable
    $currentInclude = [Environment]::GetEnvironmentVariable("INCLUDE", "User")
    if ($currentInclude) {
        if ($currentInclude -notlike "*$OPENFST_INCLUDE*") {
            $newInclude = "$currentInclude;$OPENFST_INCLUDE"
            [Environment]::SetEnvironmentVariable("INCLUDE", $newInclude, "User")
            Write-Host "Added OpenFST include directory to INCLUDE" -ForegroundColor Green
        } else {
            Write-Host "OpenFST include directory already in INCLUDE" -ForegroundColor Yellow
        }
    } else {
        [Environment]::SetEnvironmentVariable("INCLUDE", $OPENFST_INCLUDE, "User")
        Write-Host "Created INCLUDE environment variable with OpenFST" -ForegroundColor Green
    }
    
    # Get current LIB environment variable
    $currentLib = [Environment]::GetEnvironmentVariable("LIB", "User")
    if ($currentLib) {
        if ($currentLib -notlike "*$OPENFST_LIB*") {
            $newLib = "$currentLib;$OPENFST_LIB"
            [Environment]::SetEnvironmentVariable("LIB", $newLib, "User")
            Write-Host "Added OpenFST lib directory to LIB" -ForegroundColor Green
        } else {
            Write-Host "OpenFST lib directory already in LIB" -ForegroundColor Yellow
        }
    } else {
        [Environment]::SetEnvironmentVariable("LIB", $OPENFST_LIB, "User")
        Write-Host "Created LIB environment variable with OpenFST" -ForegroundColor Green
    }
    
    Write-Host "`nEnvironment variables set successfully!" -ForegroundColor Green
    Write-Host "The following environment variables have been configured:" -ForegroundColor Cyan
    Write-Host "  OPENFST_ROOT = $OPENFST_ROOT" -ForegroundColor White
    Write-Host "  OPENFST_INCLUDE = $OPENFST_INCLUDE" -ForegroundColor White
    Write-Host "  OPENFST_LIB = $OPENFST_LIB" -ForegroundColor White
    Write-Host "  PATH includes: $OPENFST_BIN" -ForegroundColor White
    Write-Host "  INCLUDE includes: $OPENFST_INCLUDE" -ForegroundColor White
    Write-Host "  LIB includes: $OPENFST_LIB" -ForegroundColor White
    
    Write-Host "`nIMPORTANT: You need to restart your terminal/IDE for the changes to take effect!" -ForegroundColor Yellow
    
} catch {
    Write-Host "Error setting environment variables: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nEnvironment setup complete!" -ForegroundColor Green
