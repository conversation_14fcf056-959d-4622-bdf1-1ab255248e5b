@echo off
REM Setup OpenFST Environment Variables (Batch version)
echo Setting up OpenFST environment variables...

set OPENFST_ROOT=f:\openfst-1.8.4
set OPENFST_INCLUDE=%OPENFST_ROOT%\src\include
set OPENFST_LIB=%OPENFST_ROOT%\build

REM Check if paths exist
if not exist "%OPENFST_ROOT%" (
    echo Error: OpenFST root directory not found: %OPENFST_ROOT%
    pause
    exit /b 1
)

if not exist "%OPENFST_INCLUDE%" (
    echo Error: OpenFST include directory not found: %OPENFST_INCLUDE%
    pause
    exit /b 1
)

if not exist "%OPENFST_LIB%" (
    echo Error: OpenFST lib directory not found: %OPENFST_LIB%
    pause
    exit /b 1
)

REM Set user environment variables
setx OPENFST_ROOT "%OPENFST_ROOT%"
setx OPENFST_INCLUDE "%OPENFST_INCLUDE%"
setx OPENFST_LIB "%OPENFST_LIB%"

REM Add to PATH
setx PATH "%PATH%;%OPENFST_LIB%"

echo.
echo Environment variables set successfully!
echo The following environment variables have been configured:
echo   OPENFST_ROOT = %OPENFST_ROOT%
echo   OPENFST_INCLUDE = %OPENFST_INCLUDE%
echo   OPENFST_LIB = %OPENFST_LIB%
echo   PATH includes: %OPENFST_LIB%
echo.
echo IMPORTANT: You need to restart your terminal/IDE for the changes to take effect!
echo.
pause
