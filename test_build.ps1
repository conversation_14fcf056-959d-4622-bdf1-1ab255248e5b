# Test OpenFST build with MSVC
Write-Host "Testing OpenFST build..." -ForegroundColor Green

# Set up paths
$OPENFST_ROOT = "f:\openfst-1.8.4"
$INCLUDE_DIR = "$OPENFST_ROOT\src\include"
$LIB_FILE = "$OPENFST_ROOT\build\fst.lib"
$VS_ROOT = "D:\Microsoft Visual Studio\2022\Community"
$MSVC_PATH = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\bin\HostX86\x64"
$MSVC_INCLUDE = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\include"
$MSVC_ATLMFC_INCLUDE = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\ATLMFC\include"
$MSVC_AUX_INCLUDE = "$VS_ROOT\VC\Auxiliary\VS\include"
$WIN_SDK_INCLUDE = "D:\Windows Kits\10\include\10.0.26100.0"
$WIN_SDK_LIB = "D:\Windows Kits\10\lib\10.0.26100.0"
$MSVC_LIB = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\lib\x64"
$MSVC_ATLMFC_LIB = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64"

# Set environment variables for MSVC
$env:PATH = "$MSVC_PATH;$env:PATH"
$env:INCLUDE = "$MSVC_INCLUDE;$MSVC_ATLMFC_INCLUDE;$MSVC_AUX_INCLUDE;$WIN_SDK_INCLUDE\ucrt;$WIN_SDK_INCLUDE\um;$WIN_SDK_INCLUDE\shared;$WIN_SDK_INCLUDE\winrt;$WIN_SDK_INCLUDE\cppwinrt"
$env:LIB = "$MSVC_LIB;$MSVC_ATLMFC_LIB;$WIN_SDK_LIB\ucrt\x64;$WIN_SDK_LIB\um\x64"

# Compiler flags
$CXXFLAGS = @(
    "/std:c++17",
    "/EHsc",
    "/MD",
    "/O2",
    "/DWIN32",
    "/D_WIN32",
    "/DFST_NO_DYNAMIC_LINKING",
    "/DNOMINMAX",
    "/I$INCLUDE_DIR",
    "/I$OPENFST_ROOT"
)

Write-Host "Compiling test program..." -ForegroundColor Yellow

# Compile test program
$cmd = "cl.exe"
$cmdArgs = $CXXFLAGS + @("test_openfst.cpp", "/Fe:test_openfst.exe", $LIB_FILE)

& $cmd $cmdArgs

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error compiling test program" -ForegroundColor Red
    exit 1
}

Write-Host "Running test program..." -ForegroundColor Yellow

# Run test program
.\test_openfst.exe

if ($LASTEXITCODE -eq 0) {
    Write-Host "Test passed! OpenFST is working correctly." -ForegroundColor Green
} else {
    Write-Host "Test failed!" -ForegroundColor Red
    exit 1
}
