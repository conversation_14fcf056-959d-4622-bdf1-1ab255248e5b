if HAVE_COMPACT
compactdir = compact
endif

if HAVE_COMPRESS
compressdir = compress
endif

if HAVE_CONST
constdir = const
endif

if HAVE_FAR
fardir = far
endif

if HAVE_FSTS
compactdir = compact
constdir = const
lineardir = linear
lookaheaddir = lookahead
ngramdir = ngram
endif

if HAVE_GRM
fardir = far
pdtdir = pdt
mpdtdir = mpdt
endif

if HAVE_LINEAR
lineardir = linear
endif

if HAVE_LOOKAHEAD
lookaheaddir = lookahead
endif

if HAVE_MPDT
pdtdir = pdt
mpdtdir = mpdt
endif

if HAVE_NGRAM
ngramdir = ngram
endif

if HAVE_PYTHON
fardir = far
pywrapfstdir = python
endif

if HAVE_PDT
pdtdir = pdt
endif

if HAVE_SPECIAL
specialdir = special
endif

SUBDIRS = $(compactdir) $(compressdir) $(constdir) $(fardir) $(lineardir)  \
          $(lookaheaddir) $(pdtdir) $(mpdtdir) $(ngramdir) $(pywrapfstdir) \
          $(specialdir)
