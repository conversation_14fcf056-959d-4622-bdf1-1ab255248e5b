# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
@HAVE_BIN_TRUE@bin_PROGRAMS = fstarcsort$(EXEEXT) fstclosure$(EXEEXT) \
@HAVE_BIN_TRUE@	fstcompile$(EXEEXT) fstcompose$(EXEEXT) \
@HAVE_BIN_TRUE@	fstconcat$(EXEEXT) fstconnect$(EXEEXT) \
@HAVE_BIN_TRUE@	fstconvert$(EXEEXT) fstdeterminize$(EXEEXT) \
@HAVE_BIN_TRUE@	fstdifference$(EXEEXT) fstdisambiguate$(EXEEXT) \
@HAVE_BIN_TRUE@	fstdraw$(EXEEXT) fstencode$(EXEEXT) \
@HAVE_BIN_TRUE@	fstepsnormalize$(EXEEXT) fstequal$(EXEEXT) \
@HAVE_BIN_TRUE@	fstequivalent$(EXEEXT) fstinfo$(EXEEXT) \
@HAVE_BIN_TRUE@	fstintersect$(EXEEXT) fstinvert$(EXEEXT) \
@HAVE_BIN_TRUE@	fstisomorphic$(EXEEXT) fstmap$(EXEEXT) \
@HAVE_BIN_TRUE@	fstminimize$(EXEEXT) fstprint$(EXEEXT) \
@HAVE_BIN_TRUE@	fstproject$(EXEEXT) fstprune$(EXEEXT) \
@HAVE_BIN_TRUE@	fstpush$(EXEEXT) fstrandgen$(EXEEXT) \
@HAVE_BIN_TRUE@	fstrelabel$(EXEEXT) fstreplace$(EXEEXT) \
@HAVE_BIN_TRUE@	fstreverse$(EXEEXT) fstreweight$(EXEEXT) \
@HAVE_BIN_TRUE@	fstrmepsilon$(EXEEXT) \
@HAVE_BIN_TRUE@	fstshortestdistance$(EXEEXT) \
@HAVE_BIN_TRUE@	fstshortestpath$(EXEEXT) fstsymbols$(EXEEXT) \
@HAVE_BIN_TRUE@	fstsynchronize$(EXEEXT) fsttopsort$(EXEEXT) \
@HAVE_BIN_TRUE@	fstunion$(EXEEXT)
subdir = src/bin
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_python_devel.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__installdirs = "$(DESTDIR)$(bindir)"
PROGRAMS = $(bin_PROGRAMS)
am__fstarcsort_SOURCES_DIST = fstarcsort.cc fstarcsort-main.cc
@HAVE_BIN_TRUE@am_fstarcsort_OBJECTS = fstarcsort.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstarcsort-main.$(OBJEXT)
fstarcsort_OBJECTS = $(am_fstarcsort_OBJECTS)
fstarcsort_LDADD = $(LDADD)
am__DEPENDENCIES_1 =
fstarcsort_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
am__fstclosure_SOURCES_DIST = fstclosure.cc fstclosure-main.cc
@HAVE_BIN_TRUE@am_fstclosure_OBJECTS = fstclosure.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstclosure-main.$(OBJEXT)
fstclosure_OBJECTS = $(am_fstclosure_OBJECTS)
fstclosure_LDADD = $(LDADD)
fstclosure_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstcompile_SOURCES_DIST = fstcompile.cc fstcompile-main.cc
@HAVE_BIN_TRUE@am_fstcompile_OBJECTS = fstcompile.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstcompile-main.$(OBJEXT)
fstcompile_OBJECTS = $(am_fstcompile_OBJECTS)
fstcompile_LDADD = $(LDADD)
fstcompile_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstcompose_SOURCES_DIST = fstcompose.cc fstcompose-main.cc
@HAVE_BIN_TRUE@am_fstcompose_OBJECTS = fstcompose.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstcompose-main.$(OBJEXT)
fstcompose_OBJECTS = $(am_fstcompose_OBJECTS)
fstcompose_LDADD = $(LDADD)
fstcompose_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstconcat_SOURCES_DIST = fstconcat.cc fstconcat-main.cc
@HAVE_BIN_TRUE@am_fstconcat_OBJECTS = fstconcat.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstconcat-main.$(OBJEXT)
fstconcat_OBJECTS = $(am_fstconcat_OBJECTS)
fstconcat_LDADD = $(LDADD)
fstconcat_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstconnect_SOURCES_DIST = fstconnect.cc fstconnect-main.cc
@HAVE_BIN_TRUE@am_fstconnect_OBJECTS = fstconnect.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstconnect-main.$(OBJEXT)
fstconnect_OBJECTS = $(am_fstconnect_OBJECTS)
fstconnect_LDADD = $(LDADD)
fstconnect_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstconvert_SOURCES_DIST = fstconvert.cc fstconvert-main.cc
@HAVE_BIN_TRUE@am_fstconvert_OBJECTS = fstconvert.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstconvert-main.$(OBJEXT)
fstconvert_OBJECTS = $(am_fstconvert_OBJECTS)
fstconvert_LDADD = $(LDADD)
fstconvert_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstdeterminize_SOURCES_DIST = fstdeterminize.cc \
	fstdeterminize-main.cc
@HAVE_BIN_TRUE@am_fstdeterminize_OBJECTS = fstdeterminize.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstdeterminize-main.$(OBJEXT)
fstdeterminize_OBJECTS = $(am_fstdeterminize_OBJECTS)
fstdeterminize_LDADD = $(LDADD)
fstdeterminize_DEPENDENCIES = ../script/libfstscript.la \
	../lib/libfst.la $(am__DEPENDENCIES_1)
am__fstdifference_SOURCES_DIST = fstdifference.cc \
	fstdifference-main.cc
@HAVE_BIN_TRUE@am_fstdifference_OBJECTS = fstdifference.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstdifference-main.$(OBJEXT)
fstdifference_OBJECTS = $(am_fstdifference_OBJECTS)
fstdifference_LDADD = $(LDADD)
fstdifference_DEPENDENCIES = ../script/libfstscript.la \
	../lib/libfst.la $(am__DEPENDENCIES_1)
am__fstdisambiguate_SOURCES_DIST = fstdisambiguate.cc \
	fstdisambiguate-main.cc
@HAVE_BIN_TRUE@am_fstdisambiguate_OBJECTS = fstdisambiguate.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstdisambiguate-main.$(OBJEXT)
fstdisambiguate_OBJECTS = $(am_fstdisambiguate_OBJECTS)
fstdisambiguate_LDADD = $(LDADD)
fstdisambiguate_DEPENDENCIES = ../script/libfstscript.la \
	../lib/libfst.la $(am__DEPENDENCIES_1)
am__fstdraw_SOURCES_DIST = fstdraw.cc fstdraw-main.cc
@HAVE_BIN_TRUE@am_fstdraw_OBJECTS = fstdraw.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstdraw-main.$(OBJEXT)
fstdraw_OBJECTS = $(am_fstdraw_OBJECTS)
fstdraw_LDADD = $(LDADD)
fstdraw_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstencode_SOURCES_DIST = fstencode.cc fstencode-main.cc
@HAVE_BIN_TRUE@am_fstencode_OBJECTS = fstencode.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstencode-main.$(OBJEXT)
fstencode_OBJECTS = $(am_fstencode_OBJECTS)
fstencode_LDADD = $(LDADD)
fstencode_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstepsnormalize_SOURCES_DIST = fstepsnormalize.cc \
	fstepsnormalize-main.cc
@HAVE_BIN_TRUE@am_fstepsnormalize_OBJECTS = fstepsnormalize.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstepsnormalize-main.$(OBJEXT)
fstepsnormalize_OBJECTS = $(am_fstepsnormalize_OBJECTS)
fstepsnormalize_LDADD = $(LDADD)
fstepsnormalize_DEPENDENCIES = ../script/libfstscript.la \
	../lib/libfst.la $(am__DEPENDENCIES_1)
am__fstequal_SOURCES_DIST = fstequal.cc fstequal-main.cc
@HAVE_BIN_TRUE@am_fstequal_OBJECTS = fstequal.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstequal-main.$(OBJEXT)
fstequal_OBJECTS = $(am_fstequal_OBJECTS)
fstequal_LDADD = $(LDADD)
fstequal_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstequivalent_SOURCES_DIST = fstequivalent.cc \
	fstequivalent-main.cc
@HAVE_BIN_TRUE@am_fstequivalent_OBJECTS = fstequivalent.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstequivalent-main.$(OBJEXT)
fstequivalent_OBJECTS = $(am_fstequivalent_OBJECTS)
fstequivalent_LDADD = $(LDADD)
fstequivalent_DEPENDENCIES = ../script/libfstscript.la \
	../lib/libfst.la $(am__DEPENDENCIES_1)
am__fstinfo_SOURCES_DIST = fstinfo.cc fstinfo-main.cc
@HAVE_BIN_TRUE@am_fstinfo_OBJECTS = fstinfo.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstinfo-main.$(OBJEXT)
fstinfo_OBJECTS = $(am_fstinfo_OBJECTS)
fstinfo_LDADD = $(LDADD)
fstinfo_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstintersect_SOURCES_DIST = fstintersect.cc fstintersect-main.cc
@HAVE_BIN_TRUE@am_fstintersect_OBJECTS = fstintersect.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstintersect-main.$(OBJEXT)
fstintersect_OBJECTS = $(am_fstintersect_OBJECTS)
fstintersect_LDADD = $(LDADD)
fstintersect_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstinvert_SOURCES_DIST = fstinvert.cc fstinvert-main.cc
@HAVE_BIN_TRUE@am_fstinvert_OBJECTS = fstinvert.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstinvert-main.$(OBJEXT)
fstinvert_OBJECTS = $(am_fstinvert_OBJECTS)
fstinvert_LDADD = $(LDADD)
fstinvert_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstisomorphic_SOURCES_DIST = fstisomorphic.cc \
	fstisomorphic-main.cc
@HAVE_BIN_TRUE@am_fstisomorphic_OBJECTS = fstisomorphic.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstisomorphic-main.$(OBJEXT)
fstisomorphic_OBJECTS = $(am_fstisomorphic_OBJECTS)
fstisomorphic_LDADD = $(LDADD)
fstisomorphic_DEPENDENCIES = ../script/libfstscript.la \
	../lib/libfst.la $(am__DEPENDENCIES_1)
am__fstmap_SOURCES_DIST = fstmap.cc fstmap-main.cc
@HAVE_BIN_TRUE@am_fstmap_OBJECTS = fstmap.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstmap-main.$(OBJEXT)
fstmap_OBJECTS = $(am_fstmap_OBJECTS)
fstmap_LDADD = $(LDADD)
fstmap_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstminimize_SOURCES_DIST = fstminimize.cc fstminimize-main.cc
@HAVE_BIN_TRUE@am_fstminimize_OBJECTS = fstminimize.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstminimize-main.$(OBJEXT)
fstminimize_OBJECTS = $(am_fstminimize_OBJECTS)
fstminimize_LDADD = $(LDADD)
fstminimize_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstprint_SOURCES_DIST = fstprint.cc fstprint-main.cc
@HAVE_BIN_TRUE@am_fstprint_OBJECTS = fstprint.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstprint-main.$(OBJEXT)
fstprint_OBJECTS = $(am_fstprint_OBJECTS)
fstprint_LDADD = $(LDADD)
fstprint_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstproject_SOURCES_DIST = fstproject.cc fstproject-main.cc
@HAVE_BIN_TRUE@am_fstproject_OBJECTS = fstproject.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstproject-main.$(OBJEXT)
fstproject_OBJECTS = $(am_fstproject_OBJECTS)
fstproject_LDADD = $(LDADD)
fstproject_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstprune_SOURCES_DIST = fstprune.cc fstprune-main.cc
@HAVE_BIN_TRUE@am_fstprune_OBJECTS = fstprune.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstprune-main.$(OBJEXT)
fstprune_OBJECTS = $(am_fstprune_OBJECTS)
fstprune_LDADD = $(LDADD)
fstprune_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstpush_SOURCES_DIST = fstpush.cc fstpush-main.cc
@HAVE_BIN_TRUE@am_fstpush_OBJECTS = fstpush.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstpush-main.$(OBJEXT)
fstpush_OBJECTS = $(am_fstpush_OBJECTS)
fstpush_LDADD = $(LDADD)
fstpush_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstrandgen_SOURCES_DIST = fstrandgen.cc fstrandgen-main.cc
@HAVE_BIN_TRUE@am_fstrandgen_OBJECTS = fstrandgen.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstrandgen-main.$(OBJEXT)
fstrandgen_OBJECTS = $(am_fstrandgen_OBJECTS)
fstrandgen_LDADD = $(LDADD)
fstrandgen_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstrelabel_SOURCES_DIST = fstrelabel.cc fstrelabel-main.cc
@HAVE_BIN_TRUE@am_fstrelabel_OBJECTS = fstrelabel.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstrelabel-main.$(OBJEXT)
fstrelabel_OBJECTS = $(am_fstrelabel_OBJECTS)
fstrelabel_LDADD = $(LDADD)
fstrelabel_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstreplace_SOURCES_DIST = fstreplace.cc fstreplace-main.cc
@HAVE_BIN_TRUE@am_fstreplace_OBJECTS = fstreplace.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstreplace-main.$(OBJEXT)
fstreplace_OBJECTS = $(am_fstreplace_OBJECTS)
fstreplace_LDADD = $(LDADD)
fstreplace_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstreverse_SOURCES_DIST = fstreverse.cc fstreverse-main.cc
@HAVE_BIN_TRUE@am_fstreverse_OBJECTS = fstreverse.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstreverse-main.$(OBJEXT)
fstreverse_OBJECTS = $(am_fstreverse_OBJECTS)
fstreverse_LDADD = $(LDADD)
fstreverse_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstreweight_SOURCES_DIST = fstreweight.cc fstreweight-main.cc
@HAVE_BIN_TRUE@am_fstreweight_OBJECTS = fstreweight.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstreweight-main.$(OBJEXT)
fstreweight_OBJECTS = $(am_fstreweight_OBJECTS)
fstreweight_LDADD = $(LDADD)
fstreweight_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstrmepsilon_SOURCES_DIST = fstrmepsilon.cc fstrmepsilon-main.cc
@HAVE_BIN_TRUE@am_fstrmepsilon_OBJECTS = fstrmepsilon.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstrmepsilon-main.$(OBJEXT)
fstrmepsilon_OBJECTS = $(am_fstrmepsilon_OBJECTS)
fstrmepsilon_LDADD = $(LDADD)
fstrmepsilon_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstshortestdistance_SOURCES_DIST = fstshortestdistance.cc \
	fstshortestdistance-main.cc
@HAVE_BIN_TRUE@am_fstshortestdistance_OBJECTS =  \
@HAVE_BIN_TRUE@	fstshortestdistance.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstshortestdistance-main.$(OBJEXT)
fstshortestdistance_OBJECTS = $(am_fstshortestdistance_OBJECTS)
fstshortestdistance_LDADD = $(LDADD)
fstshortestdistance_DEPENDENCIES = ../script/libfstscript.la \
	../lib/libfst.la $(am__DEPENDENCIES_1)
am__fstshortestpath_SOURCES_DIST = fstshortestpath.cc \
	fstshortestpath-main.cc
@HAVE_BIN_TRUE@am_fstshortestpath_OBJECTS = fstshortestpath.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstshortestpath-main.$(OBJEXT)
fstshortestpath_OBJECTS = $(am_fstshortestpath_OBJECTS)
fstshortestpath_LDADD = $(LDADD)
fstshortestpath_DEPENDENCIES = ../script/libfstscript.la \
	../lib/libfst.la $(am__DEPENDENCIES_1)
am__fstsymbols_SOURCES_DIST = fstsymbols.cc fstsymbols-main.cc
@HAVE_BIN_TRUE@am_fstsymbols_OBJECTS = fstsymbols.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstsymbols-main.$(OBJEXT)
fstsymbols_OBJECTS = $(am_fstsymbols_OBJECTS)
fstsymbols_LDADD = $(LDADD)
fstsymbols_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstsynchronize_SOURCES_DIST = fstsynchronize.cc \
	fstsynchronize-main.cc
@HAVE_BIN_TRUE@am_fstsynchronize_OBJECTS = fstsynchronize.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstsynchronize-main.$(OBJEXT)
fstsynchronize_OBJECTS = $(am_fstsynchronize_OBJECTS)
fstsynchronize_LDADD = $(LDADD)
fstsynchronize_DEPENDENCIES = ../script/libfstscript.la \
	../lib/libfst.la $(am__DEPENDENCIES_1)
am__fsttopsort_SOURCES_DIST = fsttopsort.cc fsttopsort-main.cc
@HAVE_BIN_TRUE@am_fsttopsort_OBJECTS = fsttopsort.$(OBJEXT) \
@HAVE_BIN_TRUE@	fsttopsort-main.$(OBJEXT)
fsttopsort_OBJECTS = $(am_fsttopsort_OBJECTS)
fsttopsort_LDADD = $(LDADD)
fsttopsort_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
am__fstunion_SOURCES_DIST = fstunion.cc fstunion-main.cc
@HAVE_BIN_TRUE@am_fstunion_OBJECTS = fstunion.$(OBJEXT) \
@HAVE_BIN_TRUE@	fstunion-main.$(OBJEXT)
fstunion_OBJECTS = $(am_fstunion_OBJECTS)
fstunion_LDADD = $(LDADD)
fstunion_DEPENDENCIES = ../script/libfstscript.la ../lib/libfst.la \
	$(am__DEPENDENCIES_1)
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = 
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/fstarcsort-main.Po \
	./$(DEPDIR)/fstarcsort.Po ./$(DEPDIR)/fstclosure-main.Po \
	./$(DEPDIR)/fstclosure.Po ./$(DEPDIR)/fstcompile-main.Po \
	./$(DEPDIR)/fstcompile.Po ./$(DEPDIR)/fstcompose-main.Po \
	./$(DEPDIR)/fstcompose.Po ./$(DEPDIR)/fstconcat-main.Po \
	./$(DEPDIR)/fstconcat.Po ./$(DEPDIR)/fstconnect-main.Po \
	./$(DEPDIR)/fstconnect.Po ./$(DEPDIR)/fstconvert-main.Po \
	./$(DEPDIR)/fstconvert.Po ./$(DEPDIR)/fstdeterminize-main.Po \
	./$(DEPDIR)/fstdeterminize.Po \
	./$(DEPDIR)/fstdifference-main.Po ./$(DEPDIR)/fstdifference.Po \
	./$(DEPDIR)/fstdisambiguate-main.Po \
	./$(DEPDIR)/fstdisambiguate.Po ./$(DEPDIR)/fstdraw-main.Po \
	./$(DEPDIR)/fstdraw.Po ./$(DEPDIR)/fstencode-main.Po \
	./$(DEPDIR)/fstencode.Po ./$(DEPDIR)/fstepsnormalize-main.Po \
	./$(DEPDIR)/fstepsnormalize.Po ./$(DEPDIR)/fstequal-main.Po \
	./$(DEPDIR)/fstequal.Po ./$(DEPDIR)/fstequivalent-main.Po \
	./$(DEPDIR)/fstequivalent.Po ./$(DEPDIR)/fstinfo-main.Po \
	./$(DEPDIR)/fstinfo.Po ./$(DEPDIR)/fstintersect-main.Po \
	./$(DEPDIR)/fstintersect.Po ./$(DEPDIR)/fstinvert-main.Po \
	./$(DEPDIR)/fstinvert.Po ./$(DEPDIR)/fstisomorphic-main.Po \
	./$(DEPDIR)/fstisomorphic.Po ./$(DEPDIR)/fstmap-main.Po \
	./$(DEPDIR)/fstmap.Po ./$(DEPDIR)/fstminimize-main.Po \
	./$(DEPDIR)/fstminimize.Po ./$(DEPDIR)/fstprint-main.Po \
	./$(DEPDIR)/fstprint.Po ./$(DEPDIR)/fstproject-main.Po \
	./$(DEPDIR)/fstproject.Po ./$(DEPDIR)/fstprune-main.Po \
	./$(DEPDIR)/fstprune.Po ./$(DEPDIR)/fstpush-main.Po \
	./$(DEPDIR)/fstpush.Po ./$(DEPDIR)/fstrandgen-main.Po \
	./$(DEPDIR)/fstrandgen.Po ./$(DEPDIR)/fstrelabel-main.Po \
	./$(DEPDIR)/fstrelabel.Po ./$(DEPDIR)/fstreplace-main.Po \
	./$(DEPDIR)/fstreplace.Po ./$(DEPDIR)/fstreverse-main.Po \
	./$(DEPDIR)/fstreverse.Po ./$(DEPDIR)/fstreweight-main.Po \
	./$(DEPDIR)/fstreweight.Po ./$(DEPDIR)/fstrmepsilon-main.Po \
	./$(DEPDIR)/fstrmepsilon.Po \
	./$(DEPDIR)/fstshortestdistance-main.Po \
	./$(DEPDIR)/fstshortestdistance.Po \
	./$(DEPDIR)/fstshortestpath-main.Po \
	./$(DEPDIR)/fstshortestpath.Po ./$(DEPDIR)/fstsymbols-main.Po \
	./$(DEPDIR)/fstsymbols.Po ./$(DEPDIR)/fstsynchronize-main.Po \
	./$(DEPDIR)/fstsynchronize.Po ./$(DEPDIR)/fsttopsort-main.Po \
	./$(DEPDIR)/fsttopsort.Po ./$(DEPDIR)/fstunion-main.Po \
	./$(DEPDIR)/fstunion.Po
am__mv = mv -f
CXXCOMPILE = $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS)
LTCXXCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CXXFLAGS) $(CXXFLAGS)
AM_V_CXX = $(am__v_CXX_@AM_V@)
am__v_CXX_ = $(am__v_CXX_@AM_DEFAULT_V@)
am__v_CXX_0 = @echo "  CXX     " $@;
am__v_CXX_1 = 
CXXLD = $(CXX)
CXXLINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(AM_CXXFLAGS) \
	$(CXXFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CXXLD = $(am__v_CXXLD_@AM_V@)
am__v_CXXLD_ = $(am__v_CXXLD_@AM_DEFAULT_V@)
am__v_CXXLD_0 = @echo "  CXXLD   " $@;
am__v_CXXLD_1 = 
SOURCES = $(fstarcsort_SOURCES) $(fstclosure_SOURCES) \
	$(fstcompile_SOURCES) $(fstcompose_SOURCES) \
	$(fstconcat_SOURCES) $(fstconnect_SOURCES) \
	$(fstconvert_SOURCES) $(fstdeterminize_SOURCES) \
	$(fstdifference_SOURCES) $(fstdisambiguate_SOURCES) \
	$(fstdraw_SOURCES) $(fstencode_SOURCES) \
	$(fstepsnormalize_SOURCES) $(fstequal_SOURCES) \
	$(fstequivalent_SOURCES) $(fstinfo_SOURCES) \
	$(fstintersect_SOURCES) $(fstinvert_SOURCES) \
	$(fstisomorphic_SOURCES) $(fstmap_SOURCES) \
	$(fstminimize_SOURCES) $(fstprint_SOURCES) \
	$(fstproject_SOURCES) $(fstprune_SOURCES) $(fstpush_SOURCES) \
	$(fstrandgen_SOURCES) $(fstrelabel_SOURCES) \
	$(fstreplace_SOURCES) $(fstreverse_SOURCES) \
	$(fstreweight_SOURCES) $(fstrmepsilon_SOURCES) \
	$(fstshortestdistance_SOURCES) $(fstshortestpath_SOURCES) \
	$(fstsymbols_SOURCES) $(fstsynchronize_SOURCES) \
	$(fsttopsort_SOURCES) $(fstunion_SOURCES)
DIST_SOURCES = $(am__fstarcsort_SOURCES_DIST) \
	$(am__fstclosure_SOURCES_DIST) $(am__fstcompile_SOURCES_DIST) \
	$(am__fstcompose_SOURCES_DIST) $(am__fstconcat_SOURCES_DIST) \
	$(am__fstconnect_SOURCES_DIST) $(am__fstconvert_SOURCES_DIST) \
	$(am__fstdeterminize_SOURCES_DIST) \
	$(am__fstdifference_SOURCES_DIST) \
	$(am__fstdisambiguate_SOURCES_DIST) \
	$(am__fstdraw_SOURCES_DIST) $(am__fstencode_SOURCES_DIST) \
	$(am__fstepsnormalize_SOURCES_DIST) \
	$(am__fstequal_SOURCES_DIST) $(am__fstequivalent_SOURCES_DIST) \
	$(am__fstinfo_SOURCES_DIST) $(am__fstintersect_SOURCES_DIST) \
	$(am__fstinvert_SOURCES_DIST) \
	$(am__fstisomorphic_SOURCES_DIST) $(am__fstmap_SOURCES_DIST) \
	$(am__fstminimize_SOURCES_DIST) $(am__fstprint_SOURCES_DIST) \
	$(am__fstproject_SOURCES_DIST) $(am__fstprune_SOURCES_DIST) \
	$(am__fstpush_SOURCES_DIST) $(am__fstrandgen_SOURCES_DIST) \
	$(am__fstrelabel_SOURCES_DIST) $(am__fstreplace_SOURCES_DIST) \
	$(am__fstreverse_SOURCES_DIST) $(am__fstreweight_SOURCES_DIST) \
	$(am__fstrmepsilon_SOURCES_DIST) \
	$(am__fstshortestdistance_SOURCES_DIST) \
	$(am__fstshortestpath_SOURCES_DIST) \
	$(am__fstsymbols_SOURCES_DIST) \
	$(am__fstsynchronize_SOURCES_DIST) \
	$(am__fsttopsort_SOURCES_DIST) $(am__fstunion_SOURCES_DIST)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DL_LIBS = @DL_LIBS@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
FILECMD = @FILECMD@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PYTHON = @PYTHON@
PYTHON_CPPFLAGS = @PYTHON_CPPFLAGS@
PYTHON_EXEC_PREFIX = @PYTHON_EXEC_PREFIX@
PYTHON_EXTRA_LDFLAGS = @PYTHON_EXTRA_LDFLAGS@
PYTHON_EXTRA_LIBS = @PYTHON_EXTRA_LIBS@
PYTHON_LIBS = @PYTHON_LIBS@
PYTHON_PLATFORM = @PYTHON_PLATFORM@
PYTHON_PREFIX = @PYTHON_PREFIX@
PYTHON_SITE_PKG = @PYTHON_SITE_PKG@
PYTHON_VERSION = @PYTHON_VERSION@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
libfstdir = @libfstdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
pkgpyexecdir = @pkgpyexecdir@
pkgpythondir = @pkgpythondir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
pyexecdir = @pyexecdir@
pythondir = @pythondir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
AM_CPPFLAGS = -I$(srcdir)/../include -I$(srcdir)/../script $(ICU_FLAGS)
LDADD = ../script/libfstscript.la ../lib/libfst.la -lm $(DL_LIBS)
@HAVE_BIN_TRUE@fstarcsort_SOURCES = fstarcsort.cc fstarcsort-main.cc
@HAVE_BIN_TRUE@fstclosure_SOURCES = fstclosure.cc fstclosure-main.cc
@HAVE_BIN_TRUE@fstcompile_SOURCES = fstcompile.cc fstcompile-main.cc
@HAVE_BIN_TRUE@fstcompose_SOURCES = fstcompose.cc fstcompose-main.cc
@HAVE_BIN_TRUE@fstconcat_SOURCES = fstconcat.cc fstconcat-main.cc
@HAVE_BIN_TRUE@fstconnect_SOURCES = fstconnect.cc fstconnect-main.cc
@HAVE_BIN_TRUE@fstconvert_SOURCES = fstconvert.cc fstconvert-main.cc
@HAVE_BIN_TRUE@fstdeterminize_SOURCES = fstdeterminize.cc fstdeterminize-main.cc
@HAVE_BIN_TRUE@fstdifference_SOURCES = fstdifference.cc fstdifference-main.cc
@HAVE_BIN_TRUE@fstdisambiguate_SOURCES = fstdisambiguate.cc fstdisambiguate-main.cc
@HAVE_BIN_TRUE@fstdraw_SOURCES = fstdraw.cc fstdraw-main.cc
@HAVE_BIN_TRUE@fstencode_SOURCES = fstencode.cc fstencode-main.cc
@HAVE_BIN_TRUE@fstepsnormalize_SOURCES = fstepsnormalize.cc fstepsnormalize-main.cc
@HAVE_BIN_TRUE@fstequal_SOURCES = fstequal.cc fstequal-main.cc
@HAVE_BIN_TRUE@fstequivalent_SOURCES = fstequivalent.cc fstequivalent-main.cc
@HAVE_BIN_TRUE@fstinfo_SOURCES = fstinfo.cc fstinfo-main.cc
@HAVE_BIN_TRUE@fstintersect_SOURCES = fstintersect.cc fstintersect-main.cc
@HAVE_BIN_TRUE@fstinvert_SOURCES = fstinvert.cc fstinvert-main.cc
@HAVE_BIN_TRUE@fstisomorphic_SOURCES = fstisomorphic.cc fstisomorphic-main.cc
@HAVE_BIN_TRUE@fstmap_SOURCES = fstmap.cc fstmap-main.cc
@HAVE_BIN_TRUE@fstminimize_SOURCES = fstminimize.cc fstminimize-main.cc
@HAVE_BIN_TRUE@fstprint_SOURCES = fstprint.cc fstprint-main.cc
@HAVE_BIN_TRUE@fstproject_SOURCES = fstproject.cc fstproject-main.cc
@HAVE_BIN_TRUE@fstprune_SOURCES = fstprune.cc fstprune-main.cc
@HAVE_BIN_TRUE@fstpush_SOURCES = fstpush.cc fstpush-main.cc
@HAVE_BIN_TRUE@fstrandgen_SOURCES = fstrandgen.cc fstrandgen-main.cc
@HAVE_BIN_TRUE@fstrelabel_SOURCES = fstrelabel.cc fstrelabel-main.cc
@HAVE_BIN_TRUE@fstreplace_SOURCES = fstreplace.cc fstreplace-main.cc
@HAVE_BIN_TRUE@fstreverse_SOURCES = fstreverse.cc fstreverse-main.cc
@HAVE_BIN_TRUE@fstreweight_SOURCES = fstreweight.cc fstreweight-main.cc
@HAVE_BIN_TRUE@fstrmepsilon_SOURCES = fstrmepsilon.cc fstrmepsilon-main.cc
@HAVE_BIN_TRUE@fstshortestdistance_SOURCES = fstshortestdistance.cc fstshortestdistance-main.cc
@HAVE_BIN_TRUE@fstshortestpath_SOURCES = fstshortestpath.cc fstshortestpath-main.cc
@HAVE_BIN_TRUE@fstsymbols_SOURCES = fstsymbols.cc fstsymbols-main.cc
@HAVE_BIN_TRUE@fstsynchronize_SOURCES = fstsynchronize.cc fstsynchronize-main.cc
@HAVE_BIN_TRUE@fsttopsort_SOURCES = fsttopsort.cc fsttopsort-main.cc
@HAVE_BIN_TRUE@fstunion_SOURCES = fstunion.cc fstunion-main.cc
all: all-am

.SUFFIXES:
.SUFFIXES: .cc .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign src/bin/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign src/bin/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	@list='$(bin_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

fstarcsort$(EXEEXT): $(fstarcsort_OBJECTS) $(fstarcsort_DEPENDENCIES) $(EXTRA_fstarcsort_DEPENDENCIES) 
	@rm -f fstarcsort$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstarcsort_OBJECTS) $(fstarcsort_LDADD) $(LIBS)

fstclosure$(EXEEXT): $(fstclosure_OBJECTS) $(fstclosure_DEPENDENCIES) $(EXTRA_fstclosure_DEPENDENCIES) 
	@rm -f fstclosure$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstclosure_OBJECTS) $(fstclosure_LDADD) $(LIBS)

fstcompile$(EXEEXT): $(fstcompile_OBJECTS) $(fstcompile_DEPENDENCIES) $(EXTRA_fstcompile_DEPENDENCIES) 
	@rm -f fstcompile$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstcompile_OBJECTS) $(fstcompile_LDADD) $(LIBS)

fstcompose$(EXEEXT): $(fstcompose_OBJECTS) $(fstcompose_DEPENDENCIES) $(EXTRA_fstcompose_DEPENDENCIES) 
	@rm -f fstcompose$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstcompose_OBJECTS) $(fstcompose_LDADD) $(LIBS)

fstconcat$(EXEEXT): $(fstconcat_OBJECTS) $(fstconcat_DEPENDENCIES) $(EXTRA_fstconcat_DEPENDENCIES) 
	@rm -f fstconcat$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstconcat_OBJECTS) $(fstconcat_LDADD) $(LIBS)

fstconnect$(EXEEXT): $(fstconnect_OBJECTS) $(fstconnect_DEPENDENCIES) $(EXTRA_fstconnect_DEPENDENCIES) 
	@rm -f fstconnect$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstconnect_OBJECTS) $(fstconnect_LDADD) $(LIBS)

fstconvert$(EXEEXT): $(fstconvert_OBJECTS) $(fstconvert_DEPENDENCIES) $(EXTRA_fstconvert_DEPENDENCIES) 
	@rm -f fstconvert$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstconvert_OBJECTS) $(fstconvert_LDADD) $(LIBS)

fstdeterminize$(EXEEXT): $(fstdeterminize_OBJECTS) $(fstdeterminize_DEPENDENCIES) $(EXTRA_fstdeterminize_DEPENDENCIES) 
	@rm -f fstdeterminize$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstdeterminize_OBJECTS) $(fstdeterminize_LDADD) $(LIBS)

fstdifference$(EXEEXT): $(fstdifference_OBJECTS) $(fstdifference_DEPENDENCIES) $(EXTRA_fstdifference_DEPENDENCIES) 
	@rm -f fstdifference$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstdifference_OBJECTS) $(fstdifference_LDADD) $(LIBS)

fstdisambiguate$(EXEEXT): $(fstdisambiguate_OBJECTS) $(fstdisambiguate_DEPENDENCIES) $(EXTRA_fstdisambiguate_DEPENDENCIES) 
	@rm -f fstdisambiguate$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstdisambiguate_OBJECTS) $(fstdisambiguate_LDADD) $(LIBS)

fstdraw$(EXEEXT): $(fstdraw_OBJECTS) $(fstdraw_DEPENDENCIES) $(EXTRA_fstdraw_DEPENDENCIES) 
	@rm -f fstdraw$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstdraw_OBJECTS) $(fstdraw_LDADD) $(LIBS)

fstencode$(EXEEXT): $(fstencode_OBJECTS) $(fstencode_DEPENDENCIES) $(EXTRA_fstencode_DEPENDENCIES) 
	@rm -f fstencode$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstencode_OBJECTS) $(fstencode_LDADD) $(LIBS)

fstepsnormalize$(EXEEXT): $(fstepsnormalize_OBJECTS) $(fstepsnormalize_DEPENDENCIES) $(EXTRA_fstepsnormalize_DEPENDENCIES) 
	@rm -f fstepsnormalize$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstepsnormalize_OBJECTS) $(fstepsnormalize_LDADD) $(LIBS)

fstequal$(EXEEXT): $(fstequal_OBJECTS) $(fstequal_DEPENDENCIES) $(EXTRA_fstequal_DEPENDENCIES) 
	@rm -f fstequal$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstequal_OBJECTS) $(fstequal_LDADD) $(LIBS)

fstequivalent$(EXEEXT): $(fstequivalent_OBJECTS) $(fstequivalent_DEPENDENCIES) $(EXTRA_fstequivalent_DEPENDENCIES) 
	@rm -f fstequivalent$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstequivalent_OBJECTS) $(fstequivalent_LDADD) $(LIBS)

fstinfo$(EXEEXT): $(fstinfo_OBJECTS) $(fstinfo_DEPENDENCIES) $(EXTRA_fstinfo_DEPENDENCIES) 
	@rm -f fstinfo$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstinfo_OBJECTS) $(fstinfo_LDADD) $(LIBS)

fstintersect$(EXEEXT): $(fstintersect_OBJECTS) $(fstintersect_DEPENDENCIES) $(EXTRA_fstintersect_DEPENDENCIES) 
	@rm -f fstintersect$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstintersect_OBJECTS) $(fstintersect_LDADD) $(LIBS)

fstinvert$(EXEEXT): $(fstinvert_OBJECTS) $(fstinvert_DEPENDENCIES) $(EXTRA_fstinvert_DEPENDENCIES) 
	@rm -f fstinvert$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstinvert_OBJECTS) $(fstinvert_LDADD) $(LIBS)

fstisomorphic$(EXEEXT): $(fstisomorphic_OBJECTS) $(fstisomorphic_DEPENDENCIES) $(EXTRA_fstisomorphic_DEPENDENCIES) 
	@rm -f fstisomorphic$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstisomorphic_OBJECTS) $(fstisomorphic_LDADD) $(LIBS)

fstmap$(EXEEXT): $(fstmap_OBJECTS) $(fstmap_DEPENDENCIES) $(EXTRA_fstmap_DEPENDENCIES) 
	@rm -f fstmap$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstmap_OBJECTS) $(fstmap_LDADD) $(LIBS)

fstminimize$(EXEEXT): $(fstminimize_OBJECTS) $(fstminimize_DEPENDENCIES) $(EXTRA_fstminimize_DEPENDENCIES) 
	@rm -f fstminimize$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstminimize_OBJECTS) $(fstminimize_LDADD) $(LIBS)

fstprint$(EXEEXT): $(fstprint_OBJECTS) $(fstprint_DEPENDENCIES) $(EXTRA_fstprint_DEPENDENCIES) 
	@rm -f fstprint$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstprint_OBJECTS) $(fstprint_LDADD) $(LIBS)

fstproject$(EXEEXT): $(fstproject_OBJECTS) $(fstproject_DEPENDENCIES) $(EXTRA_fstproject_DEPENDENCIES) 
	@rm -f fstproject$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstproject_OBJECTS) $(fstproject_LDADD) $(LIBS)

fstprune$(EXEEXT): $(fstprune_OBJECTS) $(fstprune_DEPENDENCIES) $(EXTRA_fstprune_DEPENDENCIES) 
	@rm -f fstprune$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstprune_OBJECTS) $(fstprune_LDADD) $(LIBS)

fstpush$(EXEEXT): $(fstpush_OBJECTS) $(fstpush_DEPENDENCIES) $(EXTRA_fstpush_DEPENDENCIES) 
	@rm -f fstpush$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstpush_OBJECTS) $(fstpush_LDADD) $(LIBS)

fstrandgen$(EXEEXT): $(fstrandgen_OBJECTS) $(fstrandgen_DEPENDENCIES) $(EXTRA_fstrandgen_DEPENDENCIES) 
	@rm -f fstrandgen$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstrandgen_OBJECTS) $(fstrandgen_LDADD) $(LIBS)

fstrelabel$(EXEEXT): $(fstrelabel_OBJECTS) $(fstrelabel_DEPENDENCIES) $(EXTRA_fstrelabel_DEPENDENCIES) 
	@rm -f fstrelabel$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstrelabel_OBJECTS) $(fstrelabel_LDADD) $(LIBS)

fstreplace$(EXEEXT): $(fstreplace_OBJECTS) $(fstreplace_DEPENDENCIES) $(EXTRA_fstreplace_DEPENDENCIES) 
	@rm -f fstreplace$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstreplace_OBJECTS) $(fstreplace_LDADD) $(LIBS)

fstreverse$(EXEEXT): $(fstreverse_OBJECTS) $(fstreverse_DEPENDENCIES) $(EXTRA_fstreverse_DEPENDENCIES) 
	@rm -f fstreverse$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstreverse_OBJECTS) $(fstreverse_LDADD) $(LIBS)

fstreweight$(EXEEXT): $(fstreweight_OBJECTS) $(fstreweight_DEPENDENCIES) $(EXTRA_fstreweight_DEPENDENCIES) 
	@rm -f fstreweight$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstreweight_OBJECTS) $(fstreweight_LDADD) $(LIBS)

fstrmepsilon$(EXEEXT): $(fstrmepsilon_OBJECTS) $(fstrmepsilon_DEPENDENCIES) $(EXTRA_fstrmepsilon_DEPENDENCIES) 
	@rm -f fstrmepsilon$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstrmepsilon_OBJECTS) $(fstrmepsilon_LDADD) $(LIBS)

fstshortestdistance$(EXEEXT): $(fstshortestdistance_OBJECTS) $(fstshortestdistance_DEPENDENCIES) $(EXTRA_fstshortestdistance_DEPENDENCIES) 
	@rm -f fstshortestdistance$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstshortestdistance_OBJECTS) $(fstshortestdistance_LDADD) $(LIBS)

fstshortestpath$(EXEEXT): $(fstshortestpath_OBJECTS) $(fstshortestpath_DEPENDENCIES) $(EXTRA_fstshortestpath_DEPENDENCIES) 
	@rm -f fstshortestpath$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstshortestpath_OBJECTS) $(fstshortestpath_LDADD) $(LIBS)

fstsymbols$(EXEEXT): $(fstsymbols_OBJECTS) $(fstsymbols_DEPENDENCIES) $(EXTRA_fstsymbols_DEPENDENCIES) 
	@rm -f fstsymbols$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstsymbols_OBJECTS) $(fstsymbols_LDADD) $(LIBS)

fstsynchronize$(EXEEXT): $(fstsynchronize_OBJECTS) $(fstsynchronize_DEPENDENCIES) $(EXTRA_fstsynchronize_DEPENDENCIES) 
	@rm -f fstsynchronize$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstsynchronize_OBJECTS) $(fstsynchronize_LDADD) $(LIBS)

fsttopsort$(EXEEXT): $(fsttopsort_OBJECTS) $(fsttopsort_DEPENDENCIES) $(EXTRA_fsttopsort_DEPENDENCIES) 
	@rm -f fsttopsort$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fsttopsort_OBJECTS) $(fsttopsort_LDADD) $(LIBS)

fstunion$(EXEEXT): $(fstunion_OBJECTS) $(fstunion_DEPENDENCIES) $(EXTRA_fstunion_DEPENDENCIES) 
	@rm -f fstunion$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(fstunion_OBJECTS) $(fstunion_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstarcsort-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstarcsort.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstclosure-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstclosure.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstcompile-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstcompile.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstcompose-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstcompose.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstconcat-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstconcat.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstconnect-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstconnect.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstconvert-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstconvert.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstdeterminize-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstdeterminize.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstdifference-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstdifference.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstdisambiguate-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstdisambiguate.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstdraw-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstdraw.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstencode-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstencode.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstepsnormalize-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstepsnormalize.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstequal-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstequal.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstequivalent-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstequivalent.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstinfo-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstinfo.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstintersect-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstintersect.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstinvert-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstinvert.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstisomorphic-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstisomorphic.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstmap-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstmap.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstminimize-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstminimize.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstprint-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstprint.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstproject-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstproject.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstprune-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstprune.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstpush-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstpush.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstrandgen-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstrandgen.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstrelabel-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstrelabel.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstreplace-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstreplace.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstreverse-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstreverse.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstreweight-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstreweight.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstrmepsilon-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstrmepsilon.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstshortestdistance-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstshortestdistance.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstshortestpath-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstshortestpath.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstsymbols-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstsymbols.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstsynchronize-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstsynchronize.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fsttopsort-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fsttopsort.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstunion-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fstunion.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.cc.o:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCXX_TRUE@	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ $<

.cc.obj:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCXX_TRUE@	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.cc.lo:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCXX_TRUE@	$(LTCXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LTCXXCOMPILE) -c -o $@ $<

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
	for dir in "$(DESTDIR)$(bindir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-binPROGRAMS clean-generic clean-libtool mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/fstarcsort-main.Po
	-rm -f ./$(DEPDIR)/fstarcsort.Po
	-rm -f ./$(DEPDIR)/fstclosure-main.Po
	-rm -f ./$(DEPDIR)/fstclosure.Po
	-rm -f ./$(DEPDIR)/fstcompile-main.Po
	-rm -f ./$(DEPDIR)/fstcompile.Po
	-rm -f ./$(DEPDIR)/fstcompose-main.Po
	-rm -f ./$(DEPDIR)/fstcompose.Po
	-rm -f ./$(DEPDIR)/fstconcat-main.Po
	-rm -f ./$(DEPDIR)/fstconcat.Po
	-rm -f ./$(DEPDIR)/fstconnect-main.Po
	-rm -f ./$(DEPDIR)/fstconnect.Po
	-rm -f ./$(DEPDIR)/fstconvert-main.Po
	-rm -f ./$(DEPDIR)/fstconvert.Po
	-rm -f ./$(DEPDIR)/fstdeterminize-main.Po
	-rm -f ./$(DEPDIR)/fstdeterminize.Po
	-rm -f ./$(DEPDIR)/fstdifference-main.Po
	-rm -f ./$(DEPDIR)/fstdifference.Po
	-rm -f ./$(DEPDIR)/fstdisambiguate-main.Po
	-rm -f ./$(DEPDIR)/fstdisambiguate.Po
	-rm -f ./$(DEPDIR)/fstdraw-main.Po
	-rm -f ./$(DEPDIR)/fstdraw.Po
	-rm -f ./$(DEPDIR)/fstencode-main.Po
	-rm -f ./$(DEPDIR)/fstencode.Po
	-rm -f ./$(DEPDIR)/fstepsnormalize-main.Po
	-rm -f ./$(DEPDIR)/fstepsnormalize.Po
	-rm -f ./$(DEPDIR)/fstequal-main.Po
	-rm -f ./$(DEPDIR)/fstequal.Po
	-rm -f ./$(DEPDIR)/fstequivalent-main.Po
	-rm -f ./$(DEPDIR)/fstequivalent.Po
	-rm -f ./$(DEPDIR)/fstinfo-main.Po
	-rm -f ./$(DEPDIR)/fstinfo.Po
	-rm -f ./$(DEPDIR)/fstintersect-main.Po
	-rm -f ./$(DEPDIR)/fstintersect.Po
	-rm -f ./$(DEPDIR)/fstinvert-main.Po
	-rm -f ./$(DEPDIR)/fstinvert.Po
	-rm -f ./$(DEPDIR)/fstisomorphic-main.Po
	-rm -f ./$(DEPDIR)/fstisomorphic.Po
	-rm -f ./$(DEPDIR)/fstmap-main.Po
	-rm -f ./$(DEPDIR)/fstmap.Po
	-rm -f ./$(DEPDIR)/fstminimize-main.Po
	-rm -f ./$(DEPDIR)/fstminimize.Po
	-rm -f ./$(DEPDIR)/fstprint-main.Po
	-rm -f ./$(DEPDIR)/fstprint.Po
	-rm -f ./$(DEPDIR)/fstproject-main.Po
	-rm -f ./$(DEPDIR)/fstproject.Po
	-rm -f ./$(DEPDIR)/fstprune-main.Po
	-rm -f ./$(DEPDIR)/fstprune.Po
	-rm -f ./$(DEPDIR)/fstpush-main.Po
	-rm -f ./$(DEPDIR)/fstpush.Po
	-rm -f ./$(DEPDIR)/fstrandgen-main.Po
	-rm -f ./$(DEPDIR)/fstrandgen.Po
	-rm -f ./$(DEPDIR)/fstrelabel-main.Po
	-rm -f ./$(DEPDIR)/fstrelabel.Po
	-rm -f ./$(DEPDIR)/fstreplace-main.Po
	-rm -f ./$(DEPDIR)/fstreplace.Po
	-rm -f ./$(DEPDIR)/fstreverse-main.Po
	-rm -f ./$(DEPDIR)/fstreverse.Po
	-rm -f ./$(DEPDIR)/fstreweight-main.Po
	-rm -f ./$(DEPDIR)/fstreweight.Po
	-rm -f ./$(DEPDIR)/fstrmepsilon-main.Po
	-rm -f ./$(DEPDIR)/fstrmepsilon.Po
	-rm -f ./$(DEPDIR)/fstshortestdistance-main.Po
	-rm -f ./$(DEPDIR)/fstshortestdistance.Po
	-rm -f ./$(DEPDIR)/fstshortestpath-main.Po
	-rm -f ./$(DEPDIR)/fstshortestpath.Po
	-rm -f ./$(DEPDIR)/fstsymbols-main.Po
	-rm -f ./$(DEPDIR)/fstsymbols.Po
	-rm -f ./$(DEPDIR)/fstsynchronize-main.Po
	-rm -f ./$(DEPDIR)/fstsynchronize.Po
	-rm -f ./$(DEPDIR)/fsttopsort-main.Po
	-rm -f ./$(DEPDIR)/fsttopsort.Po
	-rm -f ./$(DEPDIR)/fstunion-main.Po
	-rm -f ./$(DEPDIR)/fstunion.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/fstarcsort-main.Po
	-rm -f ./$(DEPDIR)/fstarcsort.Po
	-rm -f ./$(DEPDIR)/fstclosure-main.Po
	-rm -f ./$(DEPDIR)/fstclosure.Po
	-rm -f ./$(DEPDIR)/fstcompile-main.Po
	-rm -f ./$(DEPDIR)/fstcompile.Po
	-rm -f ./$(DEPDIR)/fstcompose-main.Po
	-rm -f ./$(DEPDIR)/fstcompose.Po
	-rm -f ./$(DEPDIR)/fstconcat-main.Po
	-rm -f ./$(DEPDIR)/fstconcat.Po
	-rm -f ./$(DEPDIR)/fstconnect-main.Po
	-rm -f ./$(DEPDIR)/fstconnect.Po
	-rm -f ./$(DEPDIR)/fstconvert-main.Po
	-rm -f ./$(DEPDIR)/fstconvert.Po
	-rm -f ./$(DEPDIR)/fstdeterminize-main.Po
	-rm -f ./$(DEPDIR)/fstdeterminize.Po
	-rm -f ./$(DEPDIR)/fstdifference-main.Po
	-rm -f ./$(DEPDIR)/fstdifference.Po
	-rm -f ./$(DEPDIR)/fstdisambiguate-main.Po
	-rm -f ./$(DEPDIR)/fstdisambiguate.Po
	-rm -f ./$(DEPDIR)/fstdraw-main.Po
	-rm -f ./$(DEPDIR)/fstdraw.Po
	-rm -f ./$(DEPDIR)/fstencode-main.Po
	-rm -f ./$(DEPDIR)/fstencode.Po
	-rm -f ./$(DEPDIR)/fstepsnormalize-main.Po
	-rm -f ./$(DEPDIR)/fstepsnormalize.Po
	-rm -f ./$(DEPDIR)/fstequal-main.Po
	-rm -f ./$(DEPDIR)/fstequal.Po
	-rm -f ./$(DEPDIR)/fstequivalent-main.Po
	-rm -f ./$(DEPDIR)/fstequivalent.Po
	-rm -f ./$(DEPDIR)/fstinfo-main.Po
	-rm -f ./$(DEPDIR)/fstinfo.Po
	-rm -f ./$(DEPDIR)/fstintersect-main.Po
	-rm -f ./$(DEPDIR)/fstintersect.Po
	-rm -f ./$(DEPDIR)/fstinvert-main.Po
	-rm -f ./$(DEPDIR)/fstinvert.Po
	-rm -f ./$(DEPDIR)/fstisomorphic-main.Po
	-rm -f ./$(DEPDIR)/fstisomorphic.Po
	-rm -f ./$(DEPDIR)/fstmap-main.Po
	-rm -f ./$(DEPDIR)/fstmap.Po
	-rm -f ./$(DEPDIR)/fstminimize-main.Po
	-rm -f ./$(DEPDIR)/fstminimize.Po
	-rm -f ./$(DEPDIR)/fstprint-main.Po
	-rm -f ./$(DEPDIR)/fstprint.Po
	-rm -f ./$(DEPDIR)/fstproject-main.Po
	-rm -f ./$(DEPDIR)/fstproject.Po
	-rm -f ./$(DEPDIR)/fstprune-main.Po
	-rm -f ./$(DEPDIR)/fstprune.Po
	-rm -f ./$(DEPDIR)/fstpush-main.Po
	-rm -f ./$(DEPDIR)/fstpush.Po
	-rm -f ./$(DEPDIR)/fstrandgen-main.Po
	-rm -f ./$(DEPDIR)/fstrandgen.Po
	-rm -f ./$(DEPDIR)/fstrelabel-main.Po
	-rm -f ./$(DEPDIR)/fstrelabel.Po
	-rm -f ./$(DEPDIR)/fstreplace-main.Po
	-rm -f ./$(DEPDIR)/fstreplace.Po
	-rm -f ./$(DEPDIR)/fstreverse-main.Po
	-rm -f ./$(DEPDIR)/fstreverse.Po
	-rm -f ./$(DEPDIR)/fstreweight-main.Po
	-rm -f ./$(DEPDIR)/fstreweight.Po
	-rm -f ./$(DEPDIR)/fstrmepsilon-main.Po
	-rm -f ./$(DEPDIR)/fstrmepsilon.Po
	-rm -f ./$(DEPDIR)/fstshortestdistance-main.Po
	-rm -f ./$(DEPDIR)/fstshortestdistance.Po
	-rm -f ./$(DEPDIR)/fstshortestpath-main.Po
	-rm -f ./$(DEPDIR)/fstshortestpath.Po
	-rm -f ./$(DEPDIR)/fstsymbols-main.Po
	-rm -f ./$(DEPDIR)/fstsymbols.Po
	-rm -f ./$(DEPDIR)/fstsynchronize-main.Po
	-rm -f ./$(DEPDIR)/fstsynchronize.Po
	-rm -f ./$(DEPDIR)/fsttopsort-main.Po
	-rm -f ./$(DEPDIR)/fsttopsort.Po
	-rm -f ./$(DEPDIR)/fstunion-main.Po
	-rm -f ./$(DEPDIR)/fstunion.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-binPROGRAMS clean-generic clean-libtool cscopelist-am \
	ctags ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-binPROGRAMS \
	install-data install-data-am install-dvi install-dvi-am \
	install-exec install-exec-am install-html install-html-am \
	install-info install-info-am install-man install-pdf \
	install-pdf-am install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-am uninstall uninstall-am uninstall-binPROGRAMS

.PRECIOUS: Makefile


# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
