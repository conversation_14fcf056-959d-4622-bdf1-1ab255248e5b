// Copyright 2005-2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the 'License');
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an 'AS IS' BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// See www.openfst.org for extensive documentation on this weighted
// finite-state transducer library.
//
// Applies an operation to each arc of an FST.

#include <cstring>
#include <memory>
#include <string>

#include <fst/flags.h>
#include <fst/log.h>
#include <fst/script/fst-class.h>
#include <fst/script/getters.h>
#include <fst/script/map.h>
#include <fst/script/weight-class.h>

DECLARE_double(delta);
DECLARE_string(map_type);
DECLARE_double(power);
DECLARE_string(weight);

int fstmap_main(int argc, char **argv) {
  namespace s = fst::script;
  using fst::script::FstClass;
  using fst::script::WeightClass;

  std::string usage =
      "Applies an operation to each arc of an FST.\n\n  Usage: ";
  usage += argv[0];
  usage += " [in.fst [out.fst]]\n";

  SET_FLAGS(usage.c_str(), &argc, &argv, true);
  if (argc > 3) {
    ShowUsage();
    return 1;
  }

  const std::string in_name =
      (argc > 1 && strcmp(argv[1], "-") != 0) ? argv[1] : "";
  const std::string out_name =
      (argc > 2 && strcmp(argv[2], "-") != 0) ? argv[2] : "";

  std::unique_ptr<FstClass> ifst(FstClass::Read(in_name));
  if (!ifst) return 1;

  s::MapType map_type;
  if (!s::GetMapType(FST_FLAGS_map_type, &map_type)) {
    LOG(ERROR) << argv[0] << ": Unknown or unsupported map type "
               << FST_FLAGS_map_type;
    return 1;
  }

  const auto weight_param =
      !FST_FLAGS_weight.empty()
          ? WeightClass(ifst->WeightType(), FST_FLAGS_weight)
          : (FST_FLAGS_map_type == "times"
                 ? WeightClass::One(ifst->WeightType())
                 : WeightClass::Zero(ifst->WeightType()));

  std::unique_ptr<FstClass> ofst(
      s::Map(*ifst, map_type, FST_FLAGS_delta,
             FST_FLAGS_power, weight_param));

  return !ofst->Write(out_name);
}
