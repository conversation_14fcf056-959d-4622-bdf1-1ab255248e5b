// Copyright 2005-2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the 'License');
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an 'AS IS' BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// See www.openfst.org for extensive documentation on this weighted
// finite-state transducer library.

#include <fst/flags.h>
#include <fst/weight.h>

DEFINE_double(delta, fst::kD<PERSON><PERSON>, "Comparison/quantization delta");
DEFINE_bool(push_weights, false, "Push weights");
DEFINE_bool(push_labels, false, "Push output labels");
DEFINE_bool(remove_total_weight, false,
            "Remove total weight when pushing weights");
DEFINE_bool(remove_common_affix, false,
            "Remove common prefix/suffix when pushing labels");
DEFINE_string(reweight_type, "to_initial",
              "Push/reweight to final (vs. to initial) states: one of "
              "\"to_initial\", \"to_final\"");

int fstpush_main(int argc, char **argv);

int main(int argc, char **argv) { return fstpush_main(argc, argv); }
