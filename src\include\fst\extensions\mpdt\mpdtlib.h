// Copyright 2005-2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the 'License');
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an 'AS IS' BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// See www.openfst.org for extensive documentation on this weighted
// finite-state transducer library.
//
// This is an experimental multipush-down transducer (MPDT) library. An MPDT is
// encoded as an FST, where some transitions are labeled with open or close
// parentheses, each mated pair of which is associated to one stack. To be
// interpreted as an MPDT, the parentheses within a stack must balance on a
// path.

#ifndef FST_EXTENSIONS_MPDT_MPDTLIB_H_
#define FST_EXTENSIONS_MPDT_MPDTLIB_H_

#include <fst/extensions/mpdt/compose.h>
#include <fst/extensions/mpdt/expand.h>
#include <fst/extensions/mpdt/mpdt.h>
#include <fst/extensions/mpdt/reverse.h>

#endif  // FST_EXTENSIONS_MPDT_MPDTLIB_H_
