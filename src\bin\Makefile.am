AM_CPPFLAGS = -I$(srcdir)/../include -I$(srcdir)/../script $(ICU_FLAGS)
LDADD = ../script/libfstscript.la ../lib/libfst.la -lm $(DL_LIBS)

if HAVE_BIN
bin_PROGRAMS = fstarcsort fstclosure fstcompile fstcompose fstconcat \
fstconnect fstconvert fstdeterminize fstdifference fstdisambiguate fstdraw \
fstencode fstepsnormalize fstequal fstequivalent fstinfo fstintersect \
fstinvert fstisomorphic fstmap fstminimize fstprint fstproject fstprune \
fstpush fstrandgen fstrelabel fstreplace fstreverse fstreweight fstrmepsilon \
fstshortestdistance fstshortestpath fstsymbols fstsynchronize fsttopsort \
fstunion

fstarcsort_SOURCES = fstarcsort.cc fstarcsort-main.cc

fstclosure_SOURCES = fstclosure.cc fstclosure-main.cc

fstcompile_SOURCES = fstcompile.cc fstcompile-main.cc

fstcompose_SOURCES = fstcompose.cc fstcompose-main.cc

fstconcat_SOURCES = fstconcat.cc fstconcat-main.cc

fstconnect_SOURCES = fstconnect.cc fstconnect-main.cc

fstconvert_SOURCES = fstconvert.cc fstconvert-main.cc

fstdeterminize_SOURCES = fstdeterminize.cc fstdeterminize-main.cc

fstdifference_SOURCES = fstdifference.cc fstdifference-main.cc

fstdisambiguate_SOURCES = fstdisambiguate.cc fstdisambiguate-main.cc

fstdraw_SOURCES = fstdraw.cc fstdraw-main.cc

fstencode_SOURCES = fstencode.cc fstencode-main.cc

fstepsnormalize_SOURCES = fstepsnormalize.cc fstepsnormalize-main.cc

fstequal_SOURCES = fstequal.cc fstequal-main.cc

fstequivalent_SOURCES = fstequivalent.cc fstequivalent-main.cc

fstinfo_SOURCES = fstinfo.cc fstinfo-main.cc

fstintersect_SOURCES = fstintersect.cc fstintersect-main.cc

fstinvert_SOURCES = fstinvert.cc fstinvert-main.cc

fstisomorphic_SOURCES = fstisomorphic.cc fstisomorphic-main.cc

fstmap_SOURCES = fstmap.cc fstmap-main.cc

fstminimize_SOURCES = fstminimize.cc fstminimize-main.cc

fstprint_SOURCES = fstprint.cc fstprint-main.cc

fstproject_SOURCES = fstproject.cc fstproject-main.cc

fstprune_SOURCES = fstprune.cc fstprune-main.cc

fstpush_SOURCES = fstpush.cc fstpush-main.cc

fstrandgen_SOURCES = fstrandgen.cc fstrandgen-main.cc

fstrelabel_SOURCES = fstrelabel.cc fstrelabel-main.cc

fstreplace_SOURCES = fstreplace.cc fstreplace-main.cc

fstreverse_SOURCES = fstreverse.cc fstreverse-main.cc

fstreweight_SOURCES = fstreweight.cc fstreweight-main.cc

fstrmepsilon_SOURCES = fstrmepsilon.cc fstrmepsilon-main.cc

fstshortestdistance_SOURCES = fstshortestdistance.cc fstshortestdistance-main.cc

fstshortestpath_SOURCES = fstshortestpath.cc fstshortestpath-main.cc

fstsymbols_SOURCES = fstsymbols.cc fstsymbols-main.cc

fstsynchronize_SOURCES = fstsynchronize.cc fstsynchronize-main.cc

fsttopsort_SOURCES = fsttopsort.cc fsttopsort-main.cc

fstunion_SOURCES = fstunion.cc fstunion-main.cc
endif
