// Copyright 2005-2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the 'License');
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an 'AS IS' BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// See www.openfst.org for extensive documentation on this weighted
// finite-state transducer library.

#include <fst/script/concat.h>

#include <vector>

#include <fst/properties.h>
#include <fst/script/fst-class.h>
#include <fst/script/script-impl.h>

namespace fst {
namespace script {

void Concat(MutableFstClass *fst1, const FstClass &fst2) {
  if (!internal::ArcTypesMatch(*fst1, fst2, "Concat")) {
    fst1->SetProperties(kError, kError);
    return;
  }
  FstConcatArgs1 args{fst1, fst2};
  Apply<Operation<FstConcatArgs1>>("Concat", fst1->ArcType(), &args);
}

void Concat(const FstClass &fst1, MutableFstClass *fst2) {
  if (!internal::ArcTypesMatch(fst1, *fst2, "Concat")) {
    fst2->SetProperties(kError, kError);
    return;
  }
  FstConcatArgs2 args{fst1, fst2};
  Apply<Operation<FstConcatArgs2>>("Concat", fst2->ArcType(), &args);
}

void Concat(const std::vector<FstClass *> &fsts1, MutableFstClass *fst2) {
  for (const auto *fst1 : fsts1) {
    if (!internal::ArcTypesMatch(*fst1, *fst2, "Concat")) {
      fst2->SetProperties(kError, kError);
      return;
    }
  }
  FstConcatArgs3 args{fsts1, fst2};
  Apply<Operation<FstConcatArgs3>>("Concat", fst2->ArcType(), &args);
}

REGISTER_FST_OPERATION_3ARCS(Concat, FstConcatArgs1);
REGISTER_FST_OPERATION_3ARCS(Concat, FstConcatArgs2);
REGISTER_FST_OPERATION_3ARCS(Concat, FstConcatArgs3);

}  // namespace script
}  // namespace fst
