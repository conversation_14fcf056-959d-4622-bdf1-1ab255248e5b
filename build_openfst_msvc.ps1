# Build OpenFST with MSVC on Windows
# This script compiles OpenFST using Microsoft Visual C++ compiler

Write-Host "Building OpenFST with MSVC..." -ForegroundColor Green

# Set up paths
$OPENFST_ROOT = "f:\openfst-1.8.4"
$SRC_DIR = "$OPENFST_ROOT\src"
$INCLUDE_DIR = "$SRC_DIR\include"
$LIB_DIR = "$SRC_DIR\lib"
$BUILD_DIR = "$OPENFST_ROOT\build"
$VS_ROOT = "D:\Microsoft Visual Studio\2022\Community"
$MSVC_PATH = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\bin\HostX86\x64"
$MSVC_INCLUDE = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\include"
$MSVC_ATLMFC_INCLUDE = "$VS_ROOT\VC\Tools\MSVC\14.44.35207\ATLMFC\include"
$MSVC_AUX_INCLUDE = "$VS_ROOT\VC\Auxiliary\VS\include"
$WIN_SDK_INCLUDE = "D:\Windows Kits\10\include\10.0.26100.0"

# Create build directory
if (!(Test-Path $BUILD_DIR)) {
    New-Item -ItemType Directory -Path $BUILD_DIR
}

# Set environment variables for MSVC
$env:PATH = "$MSVC_PATH;$env:PATH"
$env:INCLUDE = "$MSVC_INCLUDE;$MSVC_ATLMFC_INCLUDE;$MSVC_AUX_INCLUDE;$WIN_SDK_INCLUDE\ucrt;$WIN_SDK_INCLUDE\um;$WIN_SDK_INCLUDE\shared;$WIN_SDK_INCLUDE\winrt;$WIN_SDK_INCLUDE\cppwinrt"

# MSVC compiler flags
$CXXFLAGS = @(
    "/std:c++17",           # C++17 standard
    "/EHsc",                # Exception handling
    "/MD",                  # Multi-threaded DLL runtime
    "/O2",                  # Optimize for speed
    "/DWIN32",              # Windows platform
    "/D_WIN32",             # Windows platform
    "/DFST_NO_DYNAMIC_LINKING", # Disable dynamic linking
    "/DNOMINMAX",           # Prevent min/max macro conflicts
    "/I$INCLUDE_DIR",       # Include directory
    "/I$OPENFST_ROOT"       # Root directory for config.h
)

# Source files to compile
$LIB_SOURCES = @(
    "$LIB_DIR\compat.cc",
    "$LIB_DIR\encode.cc",
    "$LIB_DIR\flags.cc",
    "$LIB_DIR\fst.cc",
    "$LIB_DIR\fst-types.cc",
    "$LIB_DIR\mapped-file.cc",
    "$LIB_DIR\properties.cc",
    "$LIB_DIR\symbol-table.cc",
    "$LIB_DIR\symbol-table-ops.cc",
    "$LIB_DIR\weight.cc",
    "$LIB_DIR\util.cc"
)

Write-Host "Compiling library source files..." -ForegroundColor Yellow

# Compile each source file to object file
$OBJECT_FILES = @()
foreach ($source in $LIB_SOURCES) {
    $basename = [System.IO.Path]::GetFileNameWithoutExtension($source)
    $objfile = "$BUILD_DIR\$basename.obj"
    $OBJECT_FILES += $objfile

    Write-Host "Compiling $source..." -ForegroundColor Cyan

    $cmd = "cl.exe"
    $cmdArgs = $CXXFLAGS + @("/c", $source, "/Fo$objfile")

    & $cmd $cmdArgs

    if ($LASTEXITCODE -ne 0) {
        Write-Host "Error compiling $source" -ForegroundColor Red
        exit 1
    }
}

Write-Host "Creating static library..." -ForegroundColor Yellow

# Create static library
$LIBFILE = "$BUILD_DIR\fst.lib"
$cmd = "lib.exe"
$libArgs = @("/OUT:$LIBFILE") + $OBJECT_FILES

& $cmd $libArgs

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error creating library" -ForegroundColor Red
    exit 1
}

Write-Host "OpenFST library built successfully!" -ForegroundColor Green
Write-Host "Library location: $LIBFILE" -ForegroundColor Green
Write-Host "Include directory: $INCLUDE_DIR" -ForegroundColor Green
